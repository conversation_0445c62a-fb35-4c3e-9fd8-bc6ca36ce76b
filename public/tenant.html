<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>租户管理API测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }
        .container {
            display: flex;
            height: 100vh;
        }
        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #ddd;
            overflow-y: auto;
            flex-shrink: 0;
        }
        .main-content {
            flex: 1;
            background: white;
            overflow-y: auto;
            padding: 20px;
        }
        .header {
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
            margin: 0;
        }
        .config-section {
            background: #e3f2fd;
            padding: 15px;
            margin: 20px;
            border-radius: 6px;
        }
        .api-list {
            padding: 0;
        }
        .api-item {
            border-bottom: 1px solid #eee;
            cursor: pointer;
            padding: 15px 20px;
            transition: background-color 0.2s;
        }
        .api-item:hover {
            background-color: #f8f9fa;
        }
        .api-item.active {
            background-color: #e3f2fd;
            border-left: 4px solid #007bff;
        }
        .method-badge {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-size: 11px;
            font-weight: bold;
            margin-right: 8px;
            display: inline-block;
            min-width: 50px;
            text-align: center;
        }
        .method-get { background: #28a745; }
        .method-post { background: #007bff; }
        .method-put { background: #ffc107; color: #212529; }
        .method-delete { background: #dc3545; }
        .api-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .api-path {
            color: #666;
            font-size: 12px;
        }
        .api-detail {
            display: none;
        }
        .api-detail.active {
            display: block;
        }
        .api-detail h2 {
            color: #333;
            margin-top: 0;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .response-area {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .response-area pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧接口栏 -->
        <div class="sidebar">
            <h1 class="header">租户管理API</h1>

            <!-- 配置区域 -->
            <div class="config-section">
                <h3>API配置</h3>
                <div class="form-group">
                    <label for="baseUrl">API基础URL:</label>
                    <input type="text" id="baseUrl" value="http://localhost:3000/api/v3/tenant" placeholder="http://localhost:3000/api/v3/tenant">
                </div>
                <div class="form-group" style="display:none">
                    <label for="authToken">认证Token:</label>
                    <input type="text" id="authToken" placeholder="Bearer your-jwt-token">
                </div>
            </div>

            <!-- API接口列表 -->
            <div class="api-list">
                <div class="api-item active" onclick="showApiDetail('create-tenant')">
                    <div class="api-title">
                        <span class="method-badge method-post">POST</span>
                        创建租户
                    </div>
                    <div class="api-path">/</div>
                </div>

                <div class="api-item" onclick="showApiDetail('get-tenant-members')">
                    <div class="api-title">
                        <span class="method-badge method-get">GET</span>
                        获取租户成员列表
                    </div>
                    <div class="api-path">/{tenant_id}/members</div>
                </div>

                <div class="api-item" onclick="showApiDetail('get-my-tenant-members')">
                    <div class="api-title">
                        <span class="method-badge method-get">GET</span>
                        获取我的租户成员列表
                    </div>
                    <div class="api-path">/my-tenant/members</div>
                </div>

                <div class="api-item" onclick="showApiDetail('invite-user')">
                    <div class="api-title">
                        <span class="method-badge method-post">POST</span>
                        邀请用户加入租户
                    </div>
                    <div class="api-path">/{tenant_id}/members/invite</div>
                </div>

                <div class="api-item" onclick="showApiDetail('remove-member')">
                    <div class="api-title">
                        <span class="method-badge method-delete">DELETE</span>
                        移除租户成员
                    </div>
                    <div class="api-path">/{tenant_id}/members/{user_id}</div>
                </div>

                <div class="api-item" onclick="showApiDetail('join-tenant')">
                    <div class="api-title">
                        <span class="method-badge method-put">PUT</span>
                        同意加入租户
                    </div>
                    <div class="api-path">/join/{tenant_id}</div>
                </div>

                <div class="api-item" onclick="showApiDetail('create-user')">
                    <div class="api-title">
                        <span class="method-badge method-post">POST</span>
                        创建用户
                    </div>
                    <div class="api-path">/users</div>
                </div>

                <div class="api-item" onclick="showApiDetail('update-user')">
                    <div class="api-title">
                        <span class="method-badge method-put">PUT</span>
                        编辑用户信息
                    </div>
                    <div class="api-path">/users/{user_id}</div>
                </div>

                <div class="api-item" onclick="showApiDetail('update-tenant')">
                    <div class="api-title">
                        <span class="method-badge method-put">PUT</span>
                        更新租户信息
                    </div>
                    <div class="api-path">/{tenant_id}</div>
                </div>

                <div class="api-item" onclick="showApiDetail('change-password')">
                    <div class="api-title">
                        <span class="method-badge method-put">PUT</span>
                        修改用户密码
                    </div>
                    <div class="api-path">/users/{user_id}/password</div>
                </div>
            </div>
        </div>

        <!-- 右侧主操作区 -->
        <div class="main-content">
            <!-- 1. 创建租户 -->
            <div class="api-detail active" id="create-tenant">
                <h2>
                    <span class="method-badge method-post">POST</span>
                    创建租户
                </h2>
                <p><strong>接口路径:</strong> /</p>
                <p><strong>权限要求:</strong> 超级用户</p>
                <div class="form-group">
                    <label for="create_tenant_name">租户名称:</label>
                    <input type="text" id="create_tenant_name" placeholder="输入租户名称">
                </div>
                <div class="form-group">
                    <label for="create_tenant_llm_id">大语言模型ID:</label>
                    <input type="text" id="create_tenant_llm_id" placeholder="输入LLM模型ID">
                </div>
                <div class="form-group">
                    <label for="create_tenant_embd_id">嵌入模型ID:</label>
                    <input type="text" id="create_tenant_embd_id" placeholder="输入嵌入模型ID">
                </div>
                <div class="form-group">
                    <label for="create_tenant_asr_id">语音识别模型ID:</label>
                    <input type="text" id="create_tenant_asr_id" placeholder="输入ASR模型ID">
                </div>
                <div class="form-group">
                    <label for="create_tenant_img2txt_id">图像转文本模型ID:</label>
                    <input type="text" id="create_tenant_img2txt_id" placeholder="输入图像转文本模型ID">
                </div>
                <div class="form-group">
                    <label for="create_tenant_rerank_id">重排序模型ID:</label>
                    <input type="text" id="create_tenant_rerank_id" placeholder="输入重排序模型ID">
                </div>
                <div class="form-group">
                    <label for="create_tenant_tts_id">文本转语音模型ID (可选):</label>
                    <input type="text" id="create_tenant_tts_id" placeholder="输入TTS模型ID">
                </div>
                <div class="form-group">
                    <label for="create_tenant_parser_ids">解析器ID列表:</label>
                    <input type="text" id="create_tenant_parser_ids" placeholder="输入解析器ID列表">
                </div>
                <div class="form-group">
                    <label for="create_tenant_credit">初始积分:</label>
                    <input type="number" id="create_tenant_credit" value="512">
                </div>
                <div class="form-group">
                    <label for="create_tenant_max_user_count">最大用户容量:</label>
                    <input type="number" id="create_tenant_max_user_count" value="100">
                </div>
                <div class="form-group">
                    <label for="create_tenant_public_key">公钥 (可选):</label>
                    <textarea id="create_tenant_public_key" placeholder="输入公钥"></textarea>
                </div>
                <div class="form-group">
                    <label for="create_tenant_expire_time">过期时间 (可选):</label>
                    <input type="datetime-local" id="create_tenant_expire_time">
                </div>
                <button onclick="createTenant()">发送请求</button>
                <div class="response-area" id="create_tenant_response"></div>
            </div>

            <!-- 2. 获取租户成员列表 -->
            <div class="api-detail" id="get-tenant-members">
                <h2>
                    <span class="method-badge method-get">GET</span>
                    获取租户成员列表
                </h2>
                <p><strong>接口路径:</strong> /{tenant_id}/members</p>
                <div class="form-group">
                    <label for="get_members_tenant_id">租户ID:</label>
                    <input type="text" id="get_members_tenant_id" placeholder="输入租户ID">
                </div>
                <div class="form-group">
                    <label for="get_members_page">页码:</label>
                    <input type="number" id="get_members_page" value="1" min="1">
                </div>
                <div class="form-group">
                    <label for="get_members_size">每页数量:</label>
                    <input type="number" id="get_members_size" value="10" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="get_members_search">搜索关键词 (可选):</label>
                    <input type="text" id="get_members_search" placeholder="用户名/邮箱/昵称">
                </div>
                <div class="form-group">
                    <label for="get_members_order_by">排序字段:</label>
                    <select id="get_members_order_by">
                        <option value="create_at">创建时间</option>
                        <option value="username">用户名</option>
                        <option value="email">邮箱</option>
                        <option value="updated_at">更新时间</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="get_members_desc">降序排序:</label>
                    <select id="get_members_desc">
                        <option value="true">是</option>
                        <option value="false">否</option>
                    </select>
                </div>
                <button onclick="getTenantMembers()">发送请求</button>
                <div class="response-area" id="get_members_response"></div>
            </div>

            <!-- 2.5. 获取我的租户成员列表 -->
            <div class="api-detail" id="get-my-tenant-members">
                <h2>
                    <span class="method-badge method-get">GET</span>
                    获取我的租户成员列表
                </h2>
                <p><strong>接口路径:</strong> /my-tenant/members</p>
                <p><strong>说明:</strong> 获取当前用户所在租户的成员列表，无需指定租户ID</p>
                <div class="form-group">
                    <label for="get_my_members_page">页码:</label>
                    <input type="number" id="get_my_members_page" value="1" min="1">
                </div>
                <div class="form-group">
                    <label for="get_my_members_size">每页数量:</label>
                    <input type="number" id="get_my_members_size" value="10" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="get_my_members_search">搜索关键词 (可选):</label>
                    <input type="text" id="get_my_members_search" placeholder="用户名/邮箱/昵称">
                </div>
                <div class="form-group">
                    <label for="get_my_members_order_by">排序字段:</label>
                    <select id="get_my_members_order_by">
                        <option value="create_at">创建时间</option>
                        <option value="username">用户名</option>
                        <option value="email">邮箱</option>
                        <option value="updated_at">更新时间</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="get_my_members_desc">降序排序:</label>
                    <select id="get_my_members_desc">
                        <option value="true">是</option>
                        <option value="false">否</option>
                    </select>
                </div>
                <button onclick="getMyTenantMembers()">发送请求</button>
                <div class="response-area" id="get_my_members_response"></div>
            </div>

            <!-- 3. 邀请用户加入租户 -->
            <div class="api-detail" id="invite-user">
                <h2>
                    <span class="method-badge method-post">POST</span>
                    邀请用户加入租户
                </h2>
                <p><strong>接口路径:</strong> /{tenant_id}/members/invite</p>
                <div class="form-group">
                    <label for="invite_tenant_id">租户ID:</label>
                    <input type="text" id="invite_tenant_id" placeholder="输入租户ID">
                </div>
                <div class="form-group">
                    <label for="invite_email">用户邮箱:</label>
                    <input type="email" id="invite_email" placeholder="输入要邀请的用户邮箱">
                </div>
                <button onclick="inviteUser()">发送请求</button>
                <div class="response-area" id="invite_response"></div>
            </div>

            <!-- 4. 移除租户成员 -->
            <div class="api-detail" id="remove-member">
                <h2>
                    <span class="method-badge method-delete">DELETE</span>
                    移除租户成员
                </h2>
                <p><strong>接口路径:</strong> /{tenant_id}/members/{user_id}</p>
                <div class="form-group">
                    <label for="remove_tenant_id">租户ID:</label>
                    <input type="text" id="remove_tenant_id" placeholder="输入租户ID">
                </div>
                <div class="form-group">
                    <label for="remove_user_id">用户ID:</label>
                    <input type="text" id="remove_user_id" placeholder="输入要移除的用户ID">
                </div>
                <button onclick="removeMember()">发送请求</button>
                <div class="response-area" id="remove_response"></div>
            </div>

            <!-- 5. 同意加入租户 -->
            <div class="api-detail" id="join-tenant">
                <h2>
                    <span class="method-badge method-put">PUT</span>
                    同意加入租户
                </h2>
                <p><strong>接口路径:</strong> /join/{tenant_id}</p>
                <div class="form-group">
                    <label for="join_tenant_id">租户ID:</label>
                    <input type="text" id="join_tenant_id" placeholder="输入租户ID">
                </div>
                <button onclick="joinTenant()">发送请求</button>
                <div class="response-area" id="join_response"></div>
            </div>

            <!-- 6. 创建用户 -->
            <div class="api-detail" id="create-user">
                <h2>
                    <span class="method-badge method-post">POST</span>
                    创建用户
                </h2>
                <p><strong>接口路径:</strong> /users</p>
                <div class="form-group">
                    <label for="create_user_nickname">姓名:</label>
                    <input type="text" id="create_user_nickname" placeholder="输入用户姓名">
                </div>
                <div class="form-group">
                    <label for="create_user_email">邮箱:</label>
                    <input type="email" id="create_user_email" placeholder="输入用户邮箱">
                </div>
                <div class="form-group">
                    <label for="create_user_phone">手机号 (可选):</label>
                    <input type="text" id="create_user_phone" placeholder="输入手机号">
                </div>
                <div class="form-group">
                    <label for="create_user_organization_id">部门ID (可选):</label>
                    <input type="text" id="create_user_organization_id" placeholder="输入部门ID">
                </div>
                <div class="form-group">
                    <label for="create_user_role">租户角色:</label>
                    <select id="create_user_role">
                        <option value="normal">normal</option>
                        <option value="admin">admin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="create_user_password">密码 (可选，不填则自动生成):</label>
                    <input type="password" id="create_user_password" placeholder="输入密码">
                </div>
                <button onclick="createUser()">发送请求</button>
                <div class="response-area" id="create_user_response"></div>
            </div>

            <!-- 7. 编辑用户信息 -->
            <div class="api-detail" id="update-user">
                <h2>
                    <span class="method-badge method-put">PUT</span>
                    编辑用户信息
                </h2>
                <p><strong>接口路径:</strong> /users/{user_id}</p>
                <div class="form-group">
                    <label for="update_user_id">用户ID:</label>
                    <input type="text" id="update_user_id" placeholder="输入用户ID">
                </div>
                <div class="form-group">
                    <label for="update_user_nickname">姓名 (可选):</label>
                    <input type="text" id="update_user_nickname" placeholder="输入新姓名">
                </div>
                <div class="form-group">
                    <label for="update_user_email">邮箱 (可选):</label>
                    <input type="email" id="update_user_email" placeholder="输入新邮箱">
                </div>
                <div class="form-group">
                    <label for="update_user_phone">手机号 (可选):</label>
                    <input type="text" id="update_user_phone" placeholder="输入新手机号">
                </div>
                <div class="form-group">
                    <label for="update_user_role">租户角色 (可选):</label>
                    <select id="update_user_role">
                        <option value="">不修改</option>
                        <option value="owner">owner</option>
                        <option value="admin">admin</option>
                        <option value="normal">normal</option>
                    </select>
                </div>
                <button onclick="updateUser()">发送请求</button>
                <div class="response-area" id="update_user_response"></div>
            </div>

            <!-- 8. 更新租户信息 -->
            <div class="api-detail" id="update-tenant">
                <h2>
                    <span class="method-badge method-put">PUT</span>
                    更新租户信息
                </h2>
                <p><strong>接口路径:</strong> /{tenant_id}</p>
                <div class="form-group">
                    <label for="update_tenant_id">租户ID:</label>
                    <input type="text" id="update_tenant_id" placeholder="输入租户ID">
                </div>
                <div class="form-group">
                    <label for="update_tenant_asr_id">语音识别模型ID:</label>
                    <input type="text" id="update_tenant_asr_id" placeholder="输入ASR模型ID">
                </div>
                <div class="form-group">
                    <label for="update_tenant_embd_id">嵌入模型ID:</label>
                    <input type="text" id="update_tenant_embd_id" placeholder="输入嵌入模型ID">
                </div>
                <div class="form-group">
                    <label for="update_tenant_img2txt_id">图像转文本模型ID:</label>
                    <input type="text" id="update_tenant_img2txt_id" placeholder="输入图像转文本模型ID">
                </div>
                <div class="form-group">
                    <label for="update_tenant_llm_id">大语言模型ID:</label>
                    <input type="text" id="update_tenant_llm_id" placeholder="输入LLM模型ID">
                </div>
                <button onclick="updateTenant()">发送请求</button>
                <div class="response-area" id="update_tenant_response"></div>
            </div>

            <!-- 9. 修改用户密码 -->
            <div class="api-detail" id="change-password">
                <h2>
                    <span class="method-badge method-put">PUT</span>
                    修改用户密码
                </h2>
                <p><strong>接口路径:</strong> /users/{user_id}/password</p>
                <div class="form-group">
                    <label for="change_password_user_id">用户ID:</label>
                    <input type="text" id="change_password_user_id" placeholder="输入用户ID">
                </div>
                <div class="form-group">
                    <label for="change_password_new_password">新密码:</label>
                    <input type="password" id="change_password_new_password" placeholder="输入新密码">
                </div>
                <button onclick="changeUserPassword()">发送请求</button>
                <div class="response-area" id="change_password_response"></div>
            </div>
        </div>
    </div>

    <script>
        // 显示指定的API详情
        function showApiDetail(apiId) {
            // 隐藏所有API详情
            const allDetails = document.querySelectorAll('.api-detail');
            allDetails.forEach(detail => detail.classList.remove('active'));

            // 移除所有API项的active状态
            const allItems = document.querySelectorAll('.api-item');
            allItems.forEach(item => item.classList.remove('active'));

            // 显示选中的API详情
            const targetDetail = document.getElementById(apiId);
            if (targetDetail) {
                targetDetail.classList.add('active');
            }

            // 设置对应API项为active状态
            const targetItem = document.querySelector(`[onclick="showApiDetail('${apiId}')"]`);
            if (targetItem) {
                targetItem.classList.add('active');
            }
        }

        // 获取认证头
        function getAuthHeaders() {
            const token = document.getElementById('authToken').value;
            const headers = {
                'Content-Type': 'application/json'
            };
            
            if (token) {
                headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
            }
            
            return headers;
        }

        // 获取基础URL
        function getBaseUrl() {
            return document.getElementById('baseUrl').value || 'http://localhost:8000/api/v1/tenant';
        }

        // 显示响应结果
        function displayResponse(elementId, response, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<pre class="${isError ? 'error' : 'success'}">${JSON.stringify(response, null, 2)}</pre>`;
        }

        // 通用API请求函数
        async function makeRequest(url, method = 'GET', body = null) {
            try {
                const options = {
                    method,
                    headers: getAuthHeaders()
                };
                
                if (body) {
                    options.body = JSON.stringify(body);
                }
                
                const response = await fetch(url, options);
                const data = await response.json();
                
                return {
                    success: response.ok,
                    status: response.status,
                    data
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // 1. 创建租户
        async function createTenant() {
            const name = document.getElementById('create_tenant_name').value;
            const llm_id = document.getElementById('create_tenant_llm_id').value;
            const embd_id = document.getElementById('create_tenant_embd_id').value;
            const asr_id = document.getElementById('create_tenant_asr_id').value;
            const img2txt_id = document.getElementById('create_tenant_img2txt_id').value;
            const rerank_id = document.getElementById('create_tenant_rerank_id').value;
            const tts_id = document.getElementById('create_tenant_tts_id').value;
            const parser_ids = document.getElementById('create_tenant_parser_ids').value;
            const credit = parseInt(document.getElementById('create_tenant_credit').value);
            const max_user_count = parseInt(document.getElementById('create_tenant_max_user_count').value);
            const public_key = document.getElementById('create_tenant_public_key').value;
            const expire_time = document.getElementById('create_tenant_expire_time').value;
            
            if (!name || !llm_id || !embd_id || !asr_id || !img2txt_id || !rerank_id || !parser_ids) {
                alert('请填写必填字段');
                return;
            }
            
            const body = {
                name,
                llm_id,
                embd_id,
                asr_id,
                img2txt_id,
                rerank_id,
                parser_ids,
                credit,
                max_user_count
            };
            
            if (tts_id) body.tts_id = tts_id;
            if (public_key) body.public_key = public_key;
            if (expire_time) body.expire_time = expire_time;
            
            const url = `${getBaseUrl()}/`;
            const result = await makeRequest(url, 'POST', body);
            
            displayResponse('create_tenant_response', result, !result.success);
        }

        // 2. 获取租户成员列表
        async function getTenantMembers() {
            const tenantId = document.getElementById('get_members_tenant_id').value;
            const page = document.getElementById('get_members_page').value;
            const size = document.getElementById('get_members_size').value;
            const search = document.getElementById('get_members_search').value;
            const order_by = document.getElementById('get_members_order_by').value;
            const desc = document.getElementById('get_members_desc').value;
            
            if (!tenantId) {
                alert('请输入租户ID');
                return;
            }
            
            const params = new URLSearchParams({
                page,
                size,
                order_by,
                desc
            });
            
            if (search) params.append('search', search);
            
            const url = `${getBaseUrl()}/${tenantId}/members?${params}`;
            const result = await makeRequest(url);
            
            displayResponse('get_members_response', result, !result.success);
        }

        // 2.5. 获取我的租户成员列表
        async function getMyTenantMembers() {
            const page = document.getElementById('get_my_members_page').value;
            const size = document.getElementById('get_my_members_size').value;
            const search = document.getElementById('get_my_members_search').value;
            const order_by = document.getElementById('get_my_members_order_by').value;
            const desc = document.getElementById('get_my_members_desc').value;
            
            const params = new URLSearchParams({
                page,
                size,
                order_by,
                desc
            });
            
            if (search) params.append('search', search);
            
            const url = `${getBaseUrl()}/my-tenant/members?${params}`;
            const result = await makeRequest(url);
            
            displayResponse('get_my_members_response', result, !result.success);
        }

        // 3. 邀请用户加入租户
        async function inviteUser() {
            const tenantId = document.getElementById('invite_tenant_id').value;
            const email = document.getElementById('invite_email').value;
            
            if (!tenantId || !email) {
                alert('请填写租户ID和邮箱');
                return;
            }
            
            const body = { email };
            const url = `${getBaseUrl()}/${tenantId}/members/invite`;
            const result = await makeRequest(url, 'POST', body);
            
            displayResponse('invite_response', result, !result.success);
        }

        // 4. 移除租户成员
        async function removeMember() {
            const tenantId = document.getElementById('remove_tenant_id').value;
            const userId = document.getElementById('remove_user_id').value;
            
            if (!tenantId || !userId) {
                alert('请填写租户ID和用户ID');
                return;
            }
            
            const url = `${getBaseUrl()}/${tenantId}/members/${userId}`;
            const result = await makeRequest(url, 'DELETE');
            
            displayResponse('remove_response', result, !result.success);
        }

        // 5. 同意加入租户
        async function joinTenant() {
            const tenantId = document.getElementById('join_tenant_id').value;
            
            if (!tenantId) {
                alert('请输入租户ID');
                return;
            }
            
            const url = `${getBaseUrl()}/join/${tenantId}`;
            const result = await makeRequest(url, 'PUT');
            
            displayResponse('join_response', result, !result.success);
        }

        // 6. 创建用户
        async function createUser() {
            const nickname = document.getElementById('create_user_nickname').value;
            const email = document.getElementById('create_user_email').value;
            const phone = document.getElementById('create_user_phone').value;
            const organization_id = document.getElementById('create_user_organization_id').value;
            const role = document.getElementById('create_user_role').value;
            const password = document.getElementById('create_user_password').value;
            
            if (!nickname || !email) {
                alert('请填写姓名和邮箱');
                return;
            }
            
            const body = {
                nickname,
                email,
                role
            };
            
            if (phone) body.phone = phone;
            if (organization_id) body.organization_id = organization_id;
            if (password) body.password = password;
            
            const url = `${getBaseUrl()}/users`;
            const result = await makeRequest(url, 'POST', body);
            
            displayResponse('create_user_response', result, !result.success);
        }

        // 7. 编辑用户信息
        async function updateUser() {
            const userId = document.getElementById('update_user_id').value;
            const nickname = document.getElementById('update_user_nickname').value;
            const email = document.getElementById('update_user_email').value;
            const phone = document.getElementById('update_user_phone').value;
            const role = document.getElementById('update_user_role').value;
            
            if (!userId) {
                alert('请输入用户ID');
                return;
            }
            
            const body = {};
            if (nickname) body.nickname = nickname;
            if (email) body.email = email;
            if (phone) body.phone = phone;
            if (role) body.role = role;
            
            if (Object.keys(body).length === 0) {
                alert('请至少填写一个要更新的字段');
                return;
            }
            
            const url = `${getBaseUrl()}/users/${userId}`;
            const result = await makeRequest(url, 'PUT', body);
            
            displayResponse('update_user_response', result, !result.success);
        }

        // 8. 更新租户信息
        async function updateTenant() {
            const tenantId = document.getElementById('update_tenant_id').value;
            const asr_id = document.getElementById('update_tenant_asr_id').value;
            const embd_id = document.getElementById('update_tenant_embd_id').value;
            const img2txt_id = document.getElementById('update_tenant_img2txt_id').value;
            const llm_id = document.getElementById('update_tenant_llm_id').value;
            
            if (!tenantId || !asr_id || !embd_id || !img2txt_id || !llm_id) {
                alert('请填写所有必填字段');
                return;
            }
            
            const body = {
                tenant_id: tenantId,
                asr_id,
                embd_id,
                img2txt_id,
                llm_id
            };
            
            const url = `${getBaseUrl()}/${tenantId}`;
            const result = await makeRequest(url, 'PUT', body);
            
            displayResponse('update_tenant_response', result, !result.success);
        }

        // 9. 修改用户密码
        async function changeUserPassword() {
            const userId = document.getElementById('change_password_user_id').value;
            const newPassword = document.getElementById('change_password_new_password').value;
            
            if (!userId || !newPassword) {
                alert('请填写用户ID和新密码');
                return;
            }
            
            const body = {
                new_password: newPassword
            };
            
            const url = `${getBaseUrl()}/users/${userId}/password`;
            const result = await makeRequest(url, 'PUT', body);
            
            displayResponse('change_password_response', result, !result.success);
        }
    </script>
</body>
</html>