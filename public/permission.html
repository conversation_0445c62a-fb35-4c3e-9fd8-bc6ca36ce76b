<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一权限管理API测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }
        .container {
            display: flex;
            height: 100vh;
        }
        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #ddd;
            overflow-y: auto;
            flex-shrink: 0;
        }
        .main-content {
            flex: 1;
            background: white;
            overflow-y: auto;
            padding: 20px;
        }
        .header {
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
            margin: 0;
        }
        .config-section {
            background: #e3f2fd;
            padding: 15px;
            margin: 20px;
            border-radius: 6px;
        }
        .api-list {
            padding: 0;
        }
        .api-item {
            border-bottom: 1px solid #eee;
            cursor: pointer;
            padding: 15px 20px;
            transition: background-color 0.2s;
        }
        .api-item:hover {
            background-color: #f8f9fa;
        }
        .api-item.active {
            background-color: #e3f2fd;
            border-left: 4px solid #007bff;
        }
        .method-badge {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-size: 11px;
            font-weight: bold;
            margin-right: 8px;
            display: inline-block;
            min-width: 50px;
            text-align: center;
        }
        .method-get { background: #28a745; }
        .method-post { background: #007bff; }
        .method-put { background: #ffc107; color: #212529; }
        .method-delete { background: #dc3545; }
        .api-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .api-path {
            color: #666;
            font-size: 12px;
        }
        .api-detail {
            display: none;
        }
        .api-detail.active {
            display: block;
        }
        .api-detail h2 {
            color: #333;
            margin-top: 0;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .response-area {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .response-area pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧接口栏 -->
        <div class="sidebar">
            <h1 class="header">统一权限管理API</h1>

            <!-- 配置区域 -->
            <div class="config-section">
                <h3>API配置</h3>
                <div class="form-group">
                    <label for="baseUrl">API基础URL:</label>
                    <input type="text" id="baseUrl" value="http://localhost:3000/api/v3/permissions" placeholder="http://localhost:8000/api/v1/permissions">
                </div>
                <div class="form-group" style="display:none">
                    <label for="authToken">认证Token:</label>
                    <input type="text" id="authToken" placeholder="Bearer your-jwt-token">
                </div>
            </div>

            <!-- API接口列表 -->
            <div class="api-list">
                <div class="api-item active" onclick="showApiDetail('get-permissions')">
                    <div class="api-title">
                        <span class="method-badge method-get">GET</span>
                        获取资源权限列表
                    </div>
                    <div class="api-path">/{resource_type}/{resource_id}/permissions</div>
                </div>

                <div class="api-item" onclick="showApiDetail('add-permission')">
                    <div class="api-title">
                        <span class="method-badge method-post">POST</span>
                        添加资源权限
                    </div>
                    <div class="api-path">/{resource_type}/{resource_id}/permissions</div>
                </div>

                <div class="api-item" onclick="showApiDetail('update-permission')">
                    <div class="api-title">
                        <span class="method-badge method-put">PUT</span>
                        更新资源权限
                    </div>
                    <div class="api-path">/{resource_type}/{resource_id}/permissions/{permission_id}</div>
                </div>

                <div class="api-item" onclick="showApiDetail('delete-permission')">
                    <div class="api-title">
                        <span class="method-badge method-delete">DELETE</span>
                        删除资源权限
                    </div>
                    <div class="api-path">/{resource_type}/{resource_id}/permissions/{permission_id}</div>
                </div>

                <div class="api-item" onclick="showApiDetail('tenant-access')">
                    <div class="api-title">
                        <span class="method-badge method-put">PUT</span>
                        设置租户访问权限
                    </div>
                    <div class="api-path">/{resource_type}/{resource_id}/tenant-access</div>
                </div>

                <div class="api-item" onclick="showApiDetail('my-permission')">
                    <div class="api-title">
                        <span class="method-badge method-get">GET</span>
                        检查当前用户权限
                    </div>
                    <div class="api-path">/{resource_type}/{resource_id}/my-permission</div>
                </div>
            </div>
        </div>

        <!-- 右侧主操作区 -->
        <div class="main-content">
            <!-- 1. 获取资源权限列表 -->
            <div class="api-detail active" id="get-permissions">
                <h2>
                    <span class="method-badge method-get">GET</span>
                    获取资源权限列表
                </h2>
                <p><strong>接口路径:</strong> /{resource_type}/{resource_id}/permissions</p>
                <div class="form-group">
                    <label for="get_permissions_resource_type">资源类型:</label>
                    <select id="get_permissions_resource_type">
                        <option value="app">app</option>
                        <option value="knowledgebase">knowledgebase</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="get_permissions_resource_id">资源ID:</label>
                    <input type="text" id="get_permissions_resource_id" placeholder="输入资源ID">
                </div>
                <button onclick="getResourcePermissions()">发送请求</button>
                <div class="response-area" id="get_permissions_response"></div>
            </div>

            <!-- 2. 添加资源权限 -->
            <div class="api-detail" id="add-permission">
                <h2>
                    <span class="method-badge method-post">POST</span>
                    添加资源权限
                </h2>
                <p><strong>接口路径:</strong> /{resource_type}/{resource_id}/permissions</p>
                <div class="form-group">
                    <label for="add_permission_resource_type">资源类型:</label>
                    <select id="add_permission_resource_type">
                        <option value="app">app</option>
                        <option value="knowledgebase">knowledgebase</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="add_permission_resource_id">资源ID:</label>
                    <input type="text" id="add_permission_resource_id" placeholder="输入资源ID">
                </div>
                <div class="form-group">
                    <label for="add_permission_subject_type">主体类型:</label>
                    <select id="add_permission_subject_type">
                        <option value="user">user</option>
                        <option value="organization">organization</option>
                        <option value="tenant">tenant</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="add_permission_subject_id">主体ID:</label>
                    <input type="text" id="add_permission_subject_id" placeholder="输入主体ID">
                </div>
                <div class="form-group">
                    <label for="add_permission_type">权限类型:</label>
                    <select id="add_permission_type">
                        <option value="owner">owner</option>
                        <option value="admin">admin</option>
                        <option value="member">member</option>
                        <option value="none">none</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="add_permission_expire_at">过期时间 (可选):</label>
                    <input type="datetime-local" id="add_permission_expire_at">
                </div>
                <button onclick="addResourcePermission()">发送请求</button>
                <div class="response-area" id="add_permission_response"></div>
            </div>

            <!-- 3. 更新资源权限 -->
            <div class="api-detail" id="update-permission">
                <h2>
                    <span class="method-badge method-put">PUT</span>
                    更新资源权限
                </h2>
                <p><strong>接口路径:</strong> /{resource_type}/{resource_id}/permissions/{permission_id}</p>
                <div class="form-group">
                    <label for="update_permission_resource_type">资源类型:</label>
                    <select id="update_permission_resource_type">
                        <option value="app">app</option>
                        <option value="knowledgebase">knowledgebase</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="update_permission_resource_id">资源ID:</label>
                    <input type="text" id="update_permission_resource_id" placeholder="输入资源ID">
                </div>
                <div class="form-group">
                    <label for="update_permission_id">权限记录ID:</label>
                    <input type="text" id="update_permission_id" placeholder="输入权限记录ID">
                </div>
                <div class="form-group">
                    <label for="update_permission_type">权限类型 (可选):</label>
                    <select id="update_permission_type">
                        <option value="">不修改</option>
                        <option value="owner">owner</option>
                        <option value="admin">admin</option>
                        <option value="member">member</option>
                        <option value="none">none</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="update_permission_expire_at">过期时间 (可选):</label>
                    <input type="datetime-local" id="update_permission_expire_at">
                </div>
                <button onclick="updateResourcePermission()">发送请求</button>
                <div class="response-area" id="update_permission_response"></div>
            </div>

            <!-- 4. 删除资源权限 -->
            <div class="api-detail" id="delete-permission">
                <h2>
                    <span class="method-badge method-delete">DELETE</span>
                    删除资源权限
                </h2>
                <p><strong>接口路径:</strong> /{resource_type}/{resource_id}/permissions/{permission_id}</p>
                <div class="form-group">
                    <label for="delete_permission_resource_type">资源类型:</label>
                    <select id="delete_permission_resource_type">
                        <option value="app">app</option>
                        <option value="knowledgebase">knowledgebase</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="delete_permission_resource_id">资源ID:</label>
                    <input type="text" id="delete_permission_resource_id" placeholder="输入资源ID">
                </div>
                <div class="form-group">
                    <label for="delete_permission_id">权限记录ID:</label>
                    <input type="text" id="delete_permission_id" placeholder="输入权限记录ID">
                </div>
                <button onclick="deleteResourcePermission()">发送请求</button>
                <div class="response-area" id="delete_permission_response"></div>
            </div>

            <!-- 5. 设置租户访问权限 -->
            <div class="api-detail" id="tenant-access">
                <h2>
                    <span class="method-badge method-put">PUT</span>
                    设置租户访问权限
                </h2>
                <p><strong>接口路径:</strong> /{resource_type}/{resource_id}/tenant-access</p>
                <div class="form-group">
                    <label for="tenant_access_resource_type">资源类型:</label>
                    <select id="tenant_access_resource_type">
                        <option value="app">app</option>
                        <option value="knowledgebase">knowledgebase</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="tenant_access_resource_id">资源ID:</label>
                    <input type="text" id="tenant_access_resource_id" placeholder="输入资源ID">
                </div>
                <div class="form-group">
                    <label for="allow_tenant_access">允许租户访问:</label>
                    <select id="allow_tenant_access">
                        <option value="true">是</option>
                        <option value="false">否</option>
                    </select>
                </div>
                <button onclick="setTenantAccess()">发送请求</button>
                <div class="response-area" id="tenant_access_response"></div>
            </div>

            <!-- 6. 检查当前用户权限 -->
            <div class="api-detail" id="my-permission">
                <h2>
                    <span class="method-badge method-get">GET</span>
                    检查当前用户权限
                </h2>
                <p><strong>接口路径:</strong> /{resource_type}/{resource_id}/my-permission</p>
                <div class="form-group">
                    <label for="my_permission_resource_type">资源类型:</label>
                    <select id="my_permission_resource_type">
                        <option value="app">app</option>
                        <option value="knowledgebase">knowledgebase</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="my_permission_resource_id">资源ID:</label>
                    <input type="text" id="my_permission_resource_id" placeholder="输入资源ID">
                </div>
                <button onclick="getMyResourcePermission()">发送请求</button>
                <div class="response-area" id="my_permission_response"></div>
            </div>
        </div>
    </div>

    <script>
        // 显示指定的API详情
        function showApiDetail(apiId) {
            // 隐藏所有API详情
            const allDetails = document.querySelectorAll('.api-detail');
            allDetails.forEach(detail => detail.classList.remove('active'));

            // 移除所有API项的active状态
            const allItems = document.querySelectorAll('.api-item');
            allItems.forEach(item => item.classList.remove('active'));

            // 显示选中的API详情
            const targetDetail = document.getElementById(apiId);
            if (targetDetail) {
                targetDetail.classList.add('active');
            }

            // 设置对应API项为active状态
            const targetItem = document.querySelector(`[onclick="showApiDetail('${apiId}')"]`);
            if (targetItem) {
                targetItem.classList.add('active');
            }
        }

        // 获取认证头
        function getAuthHeaders() {
            const token = document.getElementById('authToken').value;
            const headers = {
                'Content-Type': 'application/json'
            };
            
            if (token) {
                headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
            }
            
            return headers;
        }

        // 获取基础URL
        function getBaseUrl() {
            return document.getElementById('baseUrl').value || 'http://localhost:8000/api/v1/permissions';
        }

        // 显示响应结果
        function displayResponse(elementId, response, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<pre class="${isError ? 'error' : 'success'}">${JSON.stringify(response, null, 2)}</pre>`;
        }

        // 通用API请求函数
        async function makeRequest(url, method = 'GET', body = null) {
            try {
                const options = {
                    method,
                    headers: getAuthHeaders()
                };
                
                if (body) {
                    options.body = JSON.stringify(body);
                }
                
                const response = await fetch(url, options);
                const data = await response.json();
                
                return {
                    success: response.ok,
                    status: response.status,
                    data
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // 1. 获取资源权限列表
        async function getResourcePermissions() {
            const resourceType = document.getElementById('get_permissions_resource_type').value;
            const resourceId = document.getElementById('get_permissions_resource_id').value;
            
            if (!resourceId) {
                alert('请输入资源ID');
                return;
            }
            
            const url = `${getBaseUrl()}/${resourceType}/${resourceId}/permissions`;
            const result = await makeRequest(url);
            
            displayResponse('get_permissions_response', result, !result.success);
        }

        // 2. 添加资源权限
        async function addResourcePermission() {
            const resourceType = document.getElementById('add_permission_resource_type').value;
            const resourceId = document.getElementById('add_permission_resource_id').value;
            const subjectType = document.getElementById('add_permission_subject_type').value;
            const subjectId = document.getElementById('add_permission_subject_id').value;
            const permissionType = document.getElementById('add_permission_type').value;
            const expireAt = document.getElementById('add_permission_expire_at').value;
            
            if (!resourceId || !subjectId) {
                alert('请输入资源ID和主体ID');
                return;
            }
            
            const body = {
                subject_type: subjectType,
                subject_id: subjectId,
                permission_type: permissionType
            };
            
            if (expireAt) {
                body.expire_at = new Date(expireAt).toISOString();
            }
            
            const url = `${getBaseUrl()}/${resourceType}/${resourceId}/permissions`;
            const result = await makeRequest(url, 'POST', body);
            
            displayResponse('add_permission_response', result, !result.success);
        }

        // 3. 更新资源权限
        async function updateResourcePermission() {
            const resourceType = document.getElementById('update_permission_resource_type').value;
            const resourceId = document.getElementById('update_permission_resource_id').value;
            const permissionId = document.getElementById('update_permission_id').value;
            const permissionType = document.getElementById('update_permission_type').value;
            const expireAt = document.getElementById('update_permission_expire_at').value;
            
            if (!resourceId || !permissionId) {
                alert('请输入资源ID和权限记录ID');
                return;
            }
            
            const body = {};
            
            if (permissionType) {
                body.permission_type = permissionType;
            }
            
            if (expireAt) {
                body.expire_at = new Date(expireAt).toISOString();
            }
            
            const url = `${getBaseUrl()}/${resourceType}/${resourceId}/permissions/${permissionId}`;
            const result = await makeRequest(url, 'PUT', body);
            
            displayResponse('update_permission_response', result, !result.success);
        }

        // 4. 删除资源权限
        async function deleteResourcePermission() {
            const resourceType = document.getElementById('delete_permission_resource_type').value;
            const resourceId = document.getElementById('delete_permission_resource_id').value;
            const permissionId = document.getElementById('delete_permission_id').value;
            
            if (!resourceId || !permissionId) {
                alert('请输入资源ID和权限记录ID');
                return;
            }
            
            if (!confirm('确定要删除这个权限记录吗？')) {
                return;
            }
            
            const url = `${getBaseUrl()}/${resourceType}/${resourceId}/permissions/${permissionId}`;
            const result = await makeRequest(url, 'DELETE');
            
            displayResponse('delete_permission_response', result, !result.success);
        }

        // 5. 设置租户访问权限
        async function setTenantAccess() {
            const resourceType = document.getElementById('tenant_access_resource_type').value;
            const resourceId = document.getElementById('tenant_access_resource_id').value;
            const allowTenantAccess = document.getElementById('allow_tenant_access').value === 'true';
            
            if (!resourceId) {
                alert('请输入资源ID');
                return;
            }
            
            const body = {
                allow_tenant_access: allowTenantAccess
            };
            
            const url = `${getBaseUrl()}/${resourceType}/${resourceId}/tenant-access`;
            const result = await makeRequest(url, 'PUT', body);
            
            displayResponse('tenant_access_response', result, !result.success);
        }

        // 6. 检查当前用户权限
        async function getMyResourcePermission() {
            const resourceType = document.getElementById('my_permission_resource_type').value;
            const resourceId = document.getElementById('my_permission_resource_id').value;
            
            if (!resourceId) {
                alert('请输入资源ID');
                return;
            }
            
            const url = `${getBaseUrl()}/${resourceType}/${resourceId}/my-permission`;
            const result = await makeRequest(url);
            
            displayResponse('my_permission_response', result, !result.success);
        }

        // 页面加载完成后默认显示第一个API
        document.addEventListener('DOMContentLoaded', function() {
            showApiDetail('get-permissions');
        });
    </script>
</body>
</html>
