<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组织管理API测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }
        .container {
            display: flex;
            height: 100vh;
        }
        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #ddd;
            overflow-y: auto;
            flex-shrink: 0;
        }
        .main-content {
            flex: 1;
            background: white;
            overflow-y: auto;
            padding: 20px;
        }
        .header {
            background: #28a745;
            color: white;
            padding: 20px;
            text-align: center;
            margin: 0;
        }
        .config-section {
            background: #e8f5e8;
            padding: 15px;
            margin: 20px;
            border-radius: 6px;
        }
        .api-list {
            padding: 0;
        }
        .api-item {
            border-bottom: 1px solid #eee;
            cursor: pointer;
            padding: 15px 20px;
            transition: background-color 0.2s;
        }
        .api-item:hover {
            background-color: #f8f9fa;
        }
        .api-item.active {
            background-color: #e8f5e8;
            border-left: 4px solid #28a745;
        }
        .method-badge {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-size: 11px;
            font-weight: bold;
            margin-right: 8px;
            display: inline-block;
            min-width: 50px;
            text-align: center;
        }
        .method-get { background: #28a745; }
        .method-post { background: #007bff; }
        .method-put { background: #ffc107; color: #212529; }
        .method-delete { background: #dc3545; }
        .api-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .api-path {
            color: #666;
            font-size: 12px;
        }
        .api-detail {
            display: none;
        }
        .api-detail.active {
            display: block;
        }
        .api-detail h2 {
            color: #333;
            margin-top: 0;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #28a745;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #218838;
        }
        .response-area {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .response-area pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧接口栏 -->
        <div class="sidebar">
            <h1 class="header">组织管理API</h1>

            <!-- 配置区域 -->
            <div class="config-section">
                <h3>API配置</h3>
                <div class="form-group">
                    <label for="baseUrl">API基础URL:</label>
                    <input type="text" id="baseUrl" value="http://localhost:8000/api/v1/organization" placeholder="http://localhost:8000/api/v1/organization">
                </div>
                <div class="form-group">
                    <label for="authToken">认证Token:</label>
                    <input type="text" id="authToken" placeholder="Bearer your-jwt-token">
                </div>
            </div>

            <!-- API接口列表 -->
            <div class="api-list">
                <div class="api-item active" onclick="showApiDetail('create-org')">
                    <div class="api-title">
                        <span class="method-badge method-post">POST</span>
                        创建组织
                    </div>
                    <div class="api-path">/</div>
                </div>

                <div class="api-item" onclick="showApiDetail('get-org-list')">
                    <div class="api-title">
                        <span class="method-badge method-get">GET</span>
                        获取组织列表
                    </div>
                    <div class="api-path">/list</div>
                </div>

                <div class="api-item" onclick="showApiDetail('get-org-tree')">
                    <div class="api-title">
                        <span class="method-badge method-get">GET</span>
                        获取组织树结构
                    </div>
                    <div class="api-path">/tree</div>
                </div>

                <div class="api-item" onclick="showApiDetail('get-org-detail')">
                    <div class="api-title">
                        <span class="method-badge method-get">GET</span>
                        获取组织详情
                    </div>
                    <div class="api-path">/{org_id}</div>
                </div>

                <div class="api-item" onclick="showApiDetail('update-org')">
                    <div class="api-title">
                        <span class="method-badge method-put">PUT</span>
                        更新组织信息
                    </div>
                    <div class="api-path">/{org_id}</div>
                </div>

                <div class="api-item" onclick="showApiDetail('delete-org')">
                    <div class="api-title">
                        <span class="method-badge method-delete">DELETE</span>
                        删除组织
                    </div>
                    <div class="api-path">/{org_id}</div>
                </div>

                <div class="api-item" onclick="showApiDetail('move-org')">
                    <div class="api-title">
                        <span class="method-badge method-put">PUT</span>
                        移动组织
                    </div>
                    <div class="api-path">/{org_id}/move</div>
                </div>

                <div class="api-item" onclick="showApiDetail('add-user-to-org')">
                    <div class="api-title">
                        <span class="method-badge method-post">POST</span>
                        添加用户到组织
                    </div>
                    <div class="api-path">/{org_id}/users</div>
                </div>

                <div class="api-item" onclick="showApiDetail('remove-user-from-org')">
                    <div class="api-title">
                        <span class="method-badge method-delete">DELETE</span>
                        从组织移除用户
                    </div>
                    <div class="api-path">/{org_id}/users/{user_id}</div>
                </div>

                <div class="api-item" onclick="showApiDetail('get-org-users')">
                    <div class="api-title">
                        <span class="method-badge method-get">GET</span>
                        获取组织用户列表
                    </div>
                    <div class="api-path">/{org_id}/users</div>
                </div>
            </div>
        </div>

        <!-- 右侧主操作区 -->
        <div class="main-content">
            <!-- 1. 创建组织 -->
            <div class="api-detail active" id="create-org">
                <h2>
                    <span class="method-badge method-post">POST</span>
                    创建组织
                </h2>
                <p><strong>接口路径:</strong> /</p>
                <div class="form-group">
                    <label for="create_org_parent_id">父组织ID (可选):</label>
                    <input type="text" id="create_org_parent_id" placeholder="输入父组织ID，留空表示根组织">
                </div>
                <div class="form-group">
                    <label for="create_org_name">组织名称:</label>
                    <input type="text" id="create_org_name" placeholder="输入组织名称" required>
                </div>
                <div class="form-group">
                    <label for="create_org_description">组织描述 (可选):</label>
                    <textarea id="create_org_description" placeholder="输入组织描述"></textarea>
                </div>
                <div class="form-group">
                    <label for="create_org_type">组织类型:</label>
                    <select id="create_org_type">
                        <option value="department">部门</option>
                        <option value="team">团队</option>
                        <option value="group">小组</option>
                        <option value="company">公司</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="create_org_sort_order">排序顺序:</label>
                    <input type="number" id="create_org_sort_order" value="0" placeholder="排序顺序">
                </div>
                <button onclick="createOrganization()">发送请求</button>
                <div class="response-area" id="create_org_response"></div>
            </div>

            <!-- 2. 获取组织列表 -->
            <div class="api-detail" id="get-org-list">
                <h2>
                    <span class="method-badge method-get">GET</span>
                    获取组织列表
                </h2>
                <p><strong>接口路径:</strong> /list</p>
                <div class="form-group">
                    <label for="get_org_list_tenant_id">租户ID (可选):</label>
                    <input type="text" id="get_org_list_tenant_id" placeholder="输入租户ID，留空使用当前租户">
                </div>
                <button onclick="getOrganizationList()">发送请求</button>
                <div class="response-area" id="get_org_list_response"></div>
            </div>

            <!-- 3. 获取组织树结构 -->
            <div class="api-detail" id="get-org-tree">
                <h2>
                    <span class="method-badge method-get">GET</span>
                    获取组织树结构
                </h2>
                <p><strong>接口路径:</strong> /tree</p>
                <div class="form-group">
                    <label for="get_org_tree_tenant_id">租户ID (可选):</label>
                    <input type="text" id="get_org_tree_tenant_id" placeholder="输入租户ID，留空使用当前租户">
                </div>
                <div class="form-group">
                    <label for="get_org_tree_root_id">根组织ID (可选):</label>
                    <input type="text" id="get_org_tree_root_id" placeholder="输入根组织ID，留空获取完整树">
                </div>
                <button onclick="getOrganizationTree()">发送请求</button>
                <div class="response-area" id="get_org_tree_response"></div>
            </div>

            <!-- 4. 获取组织详情 -->
            <div class="api-detail" id="get-org-detail">
                <h2>
                    <span class="method-badge method-get">GET</span>
                    获取组织详情
                </h2>
                <p><strong>接口路径:</strong> /{org_id}</p>
                <div class="form-group">
                    <label for="get_org_detail_id">组织ID:</label>
                    <input type="text" id="get_org_detail_id" placeholder="输入组织ID" required>
                </div>
                <button onclick="getOrganizationDetail()">发送请求</button>
                <div class="response-area" id="get_org_detail_response"></div>
            </div>

            <!-- 5. 更新组织信息 -->
            <div class="api-detail" id="update-org">
                <h2>
                    <span class="method-badge method-put">PUT</span>
                    更新组织信息
                </h2>
                <p><strong>接口路径:</strong> /{org_id}</p>
                <div class="form-group">
                    <label for="update_org_id">组织ID:</label>
                    <input type="text" id="update_org_id" placeholder="输入组织ID" required>
                </div>
                <div class="form-group">
                    <label for="update_org_name">组织名称 (可选):</label>
                    <input type="text" id="update_org_name" placeholder="输入新的组织名称">
                </div>
                <div class="form-group">
                    <label for="update_org_description">组织描述 (可选):</label>
                    <textarea id="update_org_description" placeholder="输入新的组织描述"></textarea>
                </div>
                <div class="form-group">
                    <label for="update_org_type">组织类型 (可选):</label>
                    <select id="update_org_type">
                        <option value="">不修改</option>
                        <option value="department">部门</option>
                        <option value="team">团队</option>
                        <option value="group">小组</option>
                        <option value="company">公司</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="update_org_sort_order">排序顺序 (可选):</label>
                    <input type="number" id="update_org_sort_order" placeholder="排序顺序">
                </div>
                <div class="form-group">
                    <label for="update_org_status">状态 (可选):</label>
                    <select id="update_org_status">
                        <option value="">不修改</option>
                        <option value="active">激活</option>
                        <option value="inactive">停用</option>
                        <option value="deleted">已删除</option>
                    </select>
                </div>
                <button onclick="updateOrganization()">发送请求</button>
                <div class="response-area" id="update_org_response"></div>
            </div>

            <!-- 6. 删除组织 -->
            <div class="api-detail" id="delete-org">
                <h2>
                    <span class="method-badge method-delete">DELETE</span>
                    删除组织
                </h2>
                <p><strong>接口路径:</strong> /{org_id}</p>
                <div class="form-group">
                    <label for="delete_org_id">组织ID:</label>
                    <input type="text" id="delete_org_id" placeholder="输入组织ID" required>
                </div>
                <button onclick="deleteOrganization()">发送请求</button>
                <div class="response-area" id="delete_org_response"></div>
            </div>

            <!-- 7. 移动组织 -->
            <div class="api-detail" id="move-org">
                <h2>
                    <span class="method-badge method-put">PUT</span>
                    移动组织
                </h2>
                <p><strong>接口路径:</strong> /{org_id}/move</p>
                <div class="form-group">
                    <label for="move_org_id">组织ID:</label>
                    <input type="text" id="move_org_id" placeholder="输入要移动的组织ID" required>
                </div>
                <div class="form-group">
                    <label for="move_org_new_parent_id">新父组织ID (可选):</label>
                    <input type="text" id="move_org_new_parent_id" placeholder="输入新父组织ID，留空表示移动到根级">
                </div>
                <button onclick="moveOrganization()">发送请求</button>
                <div class="response-area" id="move_org_response"></div>
            </div>

            <!-- 8. 添加用户到组织 -->
            <div class="api-detail" id="add-user-to-org">
                <h2>
                    <span class="method-badge method-post">POST</span>
                    添加用户到组织
                </h2>
                <p><strong>接口路径:</strong> /{org_id}/users</p>
                <div class="form-group">
                    <label for="add_user_org_id">组织ID:</label>
                    <input type="text" id="add_user_org_id" placeholder="输入组织ID" required>
                </div>
                <div class="form-group">
                    <label for="add_user_user_id">用户ID:</label>
                    <input type="text" id="add_user_user_id" placeholder="输入用户ID" required>
                </div>
                <div class="form-group">
                    <label for="add_user_role">用户角色:</label>
                    <select id="add_user_role">
                        <option value="org_member">组织成员</option>
                        <option value="org_admin">组织管理员</option>
                        <option value="org_owner">组织所有者</option>
                    </select>
                </div>
                <button onclick="addUserToOrganization()">发送请求</button>
                <div class="response-area" id="add_user_to_org_response"></div>
            </div>

            <!-- 9. 从组织移除用户 -->
            <div class="api-detail" id="remove-user-from-org">
                <h2>
                    <span class="method-badge method-delete">DELETE</span>
                    从组织移除用户
                </h2>
                <p><strong>接口路径:</strong> /{org_id}/users/{user_id}</p>
                <div class="form-group">
                    <label for="remove_user_org_id">组织ID:</label>
                    <input type="text" id="remove_user_org_id" placeholder="输入组织ID" required>
                </div>
                <div class="form-group">
                    <label for="remove_user_user_id">用户ID:</label>
                    <input type="text" id="remove_user_user_id" placeholder="输入用户ID" required>
                </div>
                <button onclick="removeUserFromOrganization()">发送请求</button>
                <div class="response-area" id="remove_user_from_org_response"></div>
            </div>

            <!-- 10. 获取组织用户列表 -->
            <div class="api-detail" id="get-org-users">
                <h2>
                    <span class="method-badge method-get">GET</span>
                    获取组织用户列表
                </h2>
                <p><strong>接口路径:</strong> /{org_id}/users</p>
                <div class="form-group">
                    <label for="get_org_users_id">组织ID:</label>
                    <input type="text" id="get_org_users_id" placeholder="输入组织ID" required>
                </div>
                <button onclick="getOrganizationUsers()">发送请求</button>
                <div class="response-area" id="get_org_users_response"></div>
            </div>
        </div>
    </div>

    <script>
        // 显示指定的API详情
        function showApiDetail(apiId) {
            // 隐藏所有API详情
            const allDetails = document.querySelectorAll('.api-detail');
            allDetails.forEach(detail => detail.classList.remove('active'));

            // 移除所有API项的active状态
            const allItems = document.querySelectorAll('.api-item');
            allItems.forEach(item => item.classList.remove('active'));

            // 显示选中的API详情
            const targetDetail = document.getElementById(apiId);
            if (targetDetail) {
                targetDetail.classList.add('active');
            }

            // 设置对应API项为active状态
            const targetItem = document.querySelector(`[onclick="showApiDetail('${apiId}')"]`);
            if (targetItem) {
                targetItem.classList.add('active');
            }
        }

        // 获取认证头
        function getAuthHeaders() {
            const token = document.getElementById('authToken').value;
            const headers = {
                'Content-Type': 'application/json'
            };

            if (token) {
                headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
            }

            return headers;
        }

        // 获取基础URL
        function getBaseUrl() {
            return document.getElementById('baseUrl').value || 'http://localhost:8000/api/v1/organization';
        }

        // 显示响应结果
        function displayResponse(elementId, response, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<pre class="${isError ? 'error' : 'success'}">${JSON.stringify(response, null, 2)}</pre>`;
        }

        // 通用API请求函数
        async function makeRequest(url, method = 'GET', body = null) {
            try {
                const options = {
                    method,
                    headers: getAuthHeaders()
                };

                if (body) {
                    options.body = JSON.stringify(body);
                }

                const response = await fetch(url, options);
                const data = await response.json();

                return {
                    success: response.ok,
                    status: response.status,
                    data
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // 1. 创建组织
        async function createOrganization() {
            const parentId = document.getElementById('create_org_parent_id').value;
            const name = document.getElementById('create_org_name').value;
            const description = document.getElementById('create_org_description').value;
            const orgType = document.getElementById('create_org_type').value;
            const sortOrder = parseInt(document.getElementById('create_org_sort_order').value) || 0;

            if (!name) {
                alert('请输入组织名称');
                return;
            }

            const body = {
                name: name,
                org_type: orgType,
                sort_order: sortOrder
            };

            if (parentId) {
                body.parent_id = parentId;
            }

            if (description) {
                body.description = description;
            }

            const url = `${getBaseUrl()}/`;
            const result = await makeRequest(url, 'POST', body);

            displayResponse('create_org_response', result, !result.success);
        }

        // 2. 获取组织列表
        async function getOrganizationList() {
            const tenantId = document.getElementById('get_org_list_tenant_id').value;

            let url = `${getBaseUrl()}/list`;
            if (tenantId) {
                url += `?tenant_id=${encodeURIComponent(tenantId)}`;
            }

            const result = await makeRequest(url);

            displayResponse('get_org_list_response', result, !result.success);
        }

        // 3. 获取组织树结构
        async function getOrganizationTree() {
            const tenantId = document.getElementById('get_org_tree_tenant_id').value;
            const rootId = document.getElementById('get_org_tree_root_id').value;

            let url = `${getBaseUrl()}/tree`;
            const params = [];

            if (tenantId) {
                params.push(`tenant_id=${encodeURIComponent(tenantId)}`);
            }

            if (rootId) {
                params.push(`root_id=${encodeURIComponent(rootId)}`);
            }

            if (params.length > 0) {
                url += `?${params.join('&')}`;
            }

            const result = await makeRequest(url);

            displayResponse('get_org_tree_response', result, !result.success);
        }

        // 4. 获取组织详情
        async function getOrganizationDetail() {
            const orgId = document.getElementById('get_org_detail_id').value;

            if (!orgId) {
                alert('请输入组织ID');
                return;
            }

            const url = `${getBaseUrl()}/${orgId}`;
            const result = await makeRequest(url);

            displayResponse('get_org_detail_response', result, !result.success);
        }

        // 5. 更新组织信息
        async function updateOrganization() {
            const orgId = document.getElementById('update_org_id').value;
            const name = document.getElementById('update_org_name').value;
            const description = document.getElementById('update_org_description').value;
            const orgType = document.getElementById('update_org_type').value;
            const sortOrder = document.getElementById('update_org_sort_order').value;
            const status = document.getElementById('update_org_status').value;

            if (!orgId) {
                alert('请输入组织ID');
                return;
            }

            const body = {};

            if (name) body.name = name;
            if (description) body.description = description;
            if (orgType) body.org_type = orgType;
            if (sortOrder) body.sort_order = parseInt(sortOrder);
            if (status) body.status = status;

            if (Object.keys(body).length === 0) {
                alert('请至少填写一个要更新的字段');
                return;
            }

            const url = `${getBaseUrl()}/${orgId}`;
            const result = await makeRequest(url, 'PUT', body);

            displayResponse('update_org_response', result, !result.success);
        }

        // 6. 删除组织
        async function deleteOrganization() {
            const orgId = document.getElementById('delete_org_id').value;

            if (!orgId) {
                alert('请输入组织ID');
                return;
            }

            if (!confirm('确定要删除这个组织吗？此操作不可撤销！')) {
                return;
            }

            const url = `${getBaseUrl()}/${orgId}`;
            const result = await makeRequest(url, 'DELETE');

            displayResponse('delete_org_response', result, !result.success);
        }

        // 7. 移动组织
        async function moveOrganization() {
            const orgId = document.getElementById('move_org_id').value;
            const newParentId = document.getElementById('move_org_new_parent_id').value;

            if (!orgId) {
                alert('请输入组织ID');
                return;
            }

            const body = {
                new_parent_id: newParentId || null
            };

            const url = `${getBaseUrl()}/${orgId}/move`;
            const result = await makeRequest(url, 'PUT', body);

            displayResponse('move_org_response', result, !result.success);
        }

        // 8. 添加用户到组织
        async function addUserToOrganization() {
            const orgId = document.getElementById('add_user_org_id').value;
            const userId = document.getElementById('add_user_user_id').value;
            const role = document.getElementById('add_user_role').value;

            if (!orgId || !userId) {
                alert('请输入组织ID和用户ID');
                return;
            }

            const body = {
                user_id: userId,
                role: role
            };

            const url = `${getBaseUrl()}/${orgId}/users`;
            const result = await makeRequest(url, 'POST', body);

            displayResponse('add_user_to_org_response', result, !result.success);
        }

        // 9. 从组织移除用户
        async function removeUserFromOrganization() {
            const orgId = document.getElementById('remove_user_org_id').value;
            const userId = document.getElementById('remove_user_user_id').value;

            if (!orgId || !userId) {
                alert('请输入组织ID和用户ID');
                return;
            }

            if (!confirm('确定要从组织中移除这个用户吗？')) {
                return;
            }

            const url = `${getBaseUrl()}/${orgId}/users/${userId}`;
            const result = await makeRequest(url, 'DELETE');

            displayResponse('remove_user_from_org_response', result, !result.success);
        }

        // 10. 获取组织用户列表
        async function getOrganizationUsers() {
            const orgId = document.getElementById('get_org_users_id').value;

            if (!orgId) {
                alert('请输入组织ID');
                return;
            }

            const url = `${getBaseUrl()}/${orgId}/users`;
            const result = await makeRequest(url);

            displayResponse('get_org_users_response', result, !result.success);
        }

        // 页面加载完成后默认显示第一个API
        document.addEventListener('DOMContentLoaded', function() {
            showApiDetail('create-org');
        });
    </script>
</body>
</html>
