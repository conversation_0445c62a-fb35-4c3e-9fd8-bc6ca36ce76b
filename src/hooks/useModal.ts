import { useState } from "react";

export function useModal<T>() {
  const [data, setData] = useState<T | undefined>(undefined);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const open = (data?: T) => {
    setData(data);
    setVisible(true);
  };

  const close = () => {
    setData(undefined);
    setVisible(false);
  };

  const toggle = (visible: boolean, data?: T) => {
    if (visible) {
      open(data);
    } else {
      close();
    }
  };

  return { data, visible, open, close, toggle, loading, setLoading };
}
