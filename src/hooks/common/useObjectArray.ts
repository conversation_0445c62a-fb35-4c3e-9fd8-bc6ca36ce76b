import { useState } from "react";

interface ObjectWithId {
  id: string | number;
}

export function useObjectArray<T extends ObjectWithId>() {
  const [data, setData] = useState<T[]>([]);

  const get = (id: ObjectWithId["id"]): T | null => {
    return data.find((item) => item.id === id) || null;
  };

  const add = (item: T | T[]) => {
    setData((prev) => [...prev, ...(Array.isArray(item) ? item : [item])]);
  };

  const remove = (id: ObjectWithId["id"]) => {
    setData((prev) => prev.filter((item) => item.id !== id));
  };

  const clear = () => {
    setData([]);
  };

  const update = (id: string, props: Partial<T>) => {
    setData((prev) =>
      prev.map((item) => (item.id === id ? { ...item, ...props } : item)),
    );
  };

  return {
    data,
    get,
    add,
    remove,
    clear,
    update,
  };
}
