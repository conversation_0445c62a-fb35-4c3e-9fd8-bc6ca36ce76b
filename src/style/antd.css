/* antd 修改样式 */

/* Table */
.ant-table-wrapper {
  border: 1px solid #ebecf0;
  border-radius: 8px;
  overflow: hidden;
}
.ant-table-wrapper .ant-table .ant-table-footer {
  border-top: 1px solid #ebecf0;
  background: #fff;
}
.ant-table-wrapper
  .ant-table-thead
  > tr
  > th:not(:last-child):not(.ant-table-selection-column):not(
    .ant-table-row-expand-icon-cell
  ):not([colspan])::before {
  width: 0;
}
.ant-table-wrapper .ant-table-thead > tr > th {
  font-weight: 400;
}
.ant-table-container-full-fill,
.ant-table-container-full-fill > .ant-spin-nested-loading,
.ant-table-container-full-fill > .ant-spin-nested-loading > .ant-spin-container,
.ant-table-container-full-fill
  > .ant-spin-nested-loading
  > .ant-spin-container
  > .ant-table {
  height: 100%;
}
.ant-table-container-full-fill
  > .ant-spin-nested-loading
  > .ant-spin-container
  > .ant-table {
  display: flex;
  flex-direction: column;
}
.ant-table-container-full-fill .ant-table-container {
  flex: 1;
}
