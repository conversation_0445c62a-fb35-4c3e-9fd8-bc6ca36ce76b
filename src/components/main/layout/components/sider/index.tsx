import { useContext, useMemo } from "react";
import { useLocation, useParams } from "react-router-dom";

import aiWorksLogo from "@/assets/AIWorksLogo.png";
import aiWorksLogoWithText from "@/assets/AIWorksLogoWithText.png";
import {
  AppIcon,
  ControlSquareIcon,
  DocIcon,
  GlobalIcon,
  KnowledgeIcon,
  LeftArrowIcon,
  LogoutIcon,
  ModelIcon,
  RightArrowIcon,
  SettingIcon,
  SidebarTriggerIcon,
  TargetIcon,
  WorkflowIcon,
} from "@/components/main/icon";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { AuthContext } from "@/contexts/authContext";
import { useLogout } from "@/controllers/API/queries/auth";
import { useCustomNavigate } from "@/customization/hooks/use-custom-navigate";
import { useModal } from "@/hooks/useModal";
import SettingModal from "@/modals/settingModal";
import { cn } from "@/utils/utils";
import { Avatar, Divider } from "antd";

interface MenuItem {
  title: string;
  url: string;
  icon: React.ReactNode;
}

enum MenuType {
  App = "app",
  Workflow = "workflow",
  Knowledge = "knowledge",
  Model = "model",
}

const app = {
  title: "应用管理",
  url: "/app",
  icon: <AppIcon />,
};
const workflow = {
  title: "工作流",
  url: "/workflow",
  icon: <WorkflowIcon />,
};
const knowledge = {
  title: "知识库",
  url: "/knowledge",
  icon: <KnowledgeIcon />,
};
const model = {
  title: "模型供应商",
  url: "/model",
  icon: <ModelIcon />,
};
const rootMenuItems = [app, workflow, knowledge, model];

export default function Sider() {
  const { open } = useSidebar();
  const navigate = useCustomNavigate();
  const location = useLocation();
  const { knowledgeId, appId } = useParams();

  const subMenuType: MenuType | undefined = useMemo(() => {
    if (knowledgeId) {
      return MenuType.Knowledge;
    }
    if (appId) {
      return MenuType.App;
    }
  }, [knowledgeId, appId]);

  const { userData } = useContext(AuthContext);
  const { mutate: mutationLogout } = useLogout();
  const userEditModal = useModal();

  const menuItems = useMemo(() => {
    switch (subMenuType) {
      case MenuType.App: {
        return [
          {
            title: "编排",
            url: `/app/${appId}/config`,
            icon: <ControlSquareIcon />,
          },
          {
            title: "发布",
            url: `/app/${appId}/publish`,
            icon: <GlobalIcon />,
          },
        ];
      }
      case MenuType.Knowledge: {
        return [
          {
            title: "文件列表",
            url: `/knowledge/${knowledgeId}/document`,
            icon: <DocIcon />,
          },
          {
            title: "召回测试",
            url: `/knowledge/${knowledgeId}/testing`,
            icon: <TargetIcon />,
          },
          {
            title: "配置",
            url: `/knowledge/${knowledgeId}/setting`,
            icon: <SettingIcon />,
          },
        ];
      }
      default: {
        return rootMenuItems;
      }
    }
  }, [knowledgeId, appId, subMenuType]);

  const renderBackItem = () => {
    let rootMenu: MenuItem;
    switch (subMenuType) {
      case MenuType.App: {
        rootMenu = app;
        break;
      }
      case MenuType.Knowledge: {
        rootMenu = knowledge;
        break;
      }
      default: {
        return null;
      }
    }
    return (
      <>
        <SidebarMenuItem key="/knowledge">
          <SidebarMenuButton
            className="group text-text-2 data-[active=true]:bg-gradient-menu data-[active=true]:text-primary-default hover:data-[active=true]:bg-transparent group-data-[collapsible=icon]:ml-[6px] group-data-[collapsible=icon]:!px-[6px] group-data-[collapsible=icon]:!py-2 [&:not([data-active='true'])]:hover:bg-bg-light-1"
            tooltip={rootMenu.title}
            onClick={() => navigate(rootMenu.url)}
          >
            {open ? <LeftArrowIcon className="!h-4 !w-4" /> : rootMenu.icon}
            <span className="text-text-1 group-data-[active=true]:text-primary-default">
              {rootMenu.title}
            </span>
          </SidebarMenuButton>
        </SidebarMenuItem>
        <SidebarSeparator className={cn(open ? "mx-0" : "")} />
      </>
    );
  };

  const openUserEditModal = () => {
    userEditModal.open();
  };

  const handleLogout = () => {
    mutationLogout();
  };

  return (
    <Sidebar
      collapsible="icon"
      className="rounded-xl border border-border-1 bg-bg-light-3 p-2 *:bg-bg-light-3 *:!shadow-none"
    >
      <SidebarHeader className="group-data-[collapsible=icon]:px-3.5 group-data-[collapsible=icon]:py-2">
        <SidebarMenu>
          <SidebarMenuItem key="AIWorks-header">
            <SidebarMenuButton
              size="lg"
              className="justify-between pl-0 hover:bg-transparent"
            >
              {open ? (
                <>
                  <img
                    src={aiWorksLogoWithText}
                    alt="AIWorks Logo"
                    className="h-[36px]"
                    onClick={() => navigate("/")}
                  />
                  <SidebarTrigger className="hover:bg-transparent">
                    <SidebarTriggerIcon
                      className="text-2xl text-text-2"
                      svgStyle={{ width: 24, height: 24 }}
                    />
                  </SidebarTrigger>
                </>
              ) : (
                <img
                  src={aiWorksLogo}
                  alt="AIWorks Logo"
                  className="h-full w-full"
                  onClick={() => navigate("/")}
                />
              )}
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="gap-2">
              {!open && (
                <SidebarMenuItem
                  key="sidebar-trigger"
                  className="sidebar-trigger-item"
                >
                  <SidebarMenuButton
                    size="lg"
                    className="ml-[6px] hover:bg-bg-light-1"
                  >
                    <SidebarTrigger className="pl-[7px] hover:bg-bg-light-1">
                      <SidebarTriggerIcon
                        className="text-2xl text-text-2"
                        svgStyle={{ width: 24, height: 24 }}
                      />
                    </SidebarTrigger>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              )}
              {renderBackItem()}
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.url}>
                  <SidebarMenuButton
                    className="group text-text-3 hover:text-text-3 data-[active=true]:bg-gradient-menu data-[active=true]:text-primary-default hover:data-[active=true]:bg-transparent group-data-[collapsible=icon]:ml-[6px] group-data-[collapsible=icon]:!px-[6px] group-data-[collapsible=icon]:!py-2 [&:not([data-active='true'])]:hover:bg-bg-light-1"
                    tooltip={item.title}
                    onClick={() => navigate(item.url)}
                    isActive={location.pathname.startsWith(item.url)}
                  >
                    {item.icon}
                    <span className="text-text-1 group-data-[active=true]:text-primary-default">
                      {item.title}
                    </span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter className="border-t border-border-1 pt-4">
        <Popover>
          <PopoverTrigger asChild>
            <div className="flex cursor-pointer items-center gap-1 rounded-md px-2 py-[5px] hover:bg-bg-light-1">
              <Avatar
                className="flex h-[20px] w-[20px] items-center justify-center rounded-[20px] border border-border-1 bg-bg-green-1 text-[10px] text-primary-default"
                style={{ fontSize: 14 }}
                size={20}
                src={userData?.avatar}
              >
                {userData?.nickname?.slice(0, 1)?.toUpperCase()}
              </Avatar>
              {open && (
                <>
                  <span className="ml-2 text-[14px] text-text-1">
                    {userData?.nickname}
                  </span>
                  <span className="absolute right-6 float-right text-lg text-text-2">
                    <RightArrowIcon />
                  </span>
                </>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent
            className="flex w-32 flex-col gap-1 rounded-lg bg-bg-light-1 px-2 py-1 shadow-node-message"
            side="right"
            align="end"
            alignOffset={-18}
          >
            <div
              className="flex cursor-pointer items-center gap-2 bg-bg-light-1 px-2 py-[5px] text-text-1 hover:bg-bg-light-3"
              onClick={openUserEditModal}
            >
              <SettingIcon className="text-xl text-text-3" />
              <span className="text-text-1 group-data-[active=true]:text-primary-default">
                设置
              </span>
            </div>
            <Divider className="my-0.5" />
            <div
              className="flex cursor-pointer items-center gap-2 bg-bg-light-1 px-2 py-[5px] text-text-1 hover:bg-bg-light-3"
              onClick={handleLogout}
            >
              <LogoutIcon className="text-xl text-text-3" />
              <span className="text-text-1 group-data-[active=true]:text-primary-default">
                退出登录
              </span>
            </div>
          </PopoverContent>
        </Popover>
      </SidebarFooter>

      <SettingModal
        open={userEditModal.visible}
        setOpen={(open) => userEditModal.toggle(open)}
      />
    </Sidebar>
  );
}
