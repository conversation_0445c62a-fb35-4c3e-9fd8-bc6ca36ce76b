export interface FileItem {
  id: string;
  name: string;
  size: number;
  type: string;
  file: File;
  progress: number;
  extension: string;
  error?: boolean;
  uploaded?: boolean;
}

// 文件类型配置
export interface FileTypeConfig {
  allowedTypes: readonly string[];
  allowedExtensions: readonly string[];
  maxFileSize: number;
  maxFiles?: number;
  description: readonly string[];
  errorMessage: string;
}

// 预定义的文件类型配置
export const FILE_TYPE_CONFIGS = {
  DOCUMENT: {
    allowedTypes: [
      "text/plain", // txt
      "text/markdown", // md
      "application/pdf", // pdf
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // xlsx
      "application/vnd.ms-excel", // xls
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // docx
      "application/msword", // doc
    ],
    allowedExtensions: [
      ".txt",
      ".md",
      ".pdf",
      ".xlsx",
      ".xls",
      ".docx",
      ".doc",
    ],
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 10,
    description: [
      "支持以下文件格式：",
      "PDF、Word(DOC, DOCX)、Excel(XLS, XLSX)、TXT、MARKDOWN",
      "单个文件大小不超过 10 MB",
    ],
    errorMessage:
      "不支持的文件类型. 仅支持 TXT, MARKDOWN, PDF, XLSX, XLS, DOC, DOCX",
  },
  IMAGE: {
    allowedTypes: [
      "image/jpeg", // jpeg
      "image/jpg", // jpg
      "image/png", // png
    ],
    allowedExtensions: [".jpeg", ".jpg", ".png"],
    maxFileSize: 5 * 1024 * 1024, // 5MB
    maxFiles: 1,
    description: [
      "支持以下文件格式：",
      "JEPG、JPG、PNG",
      "单个文件大小不超过 5 MB",
    ],
    errorMessage: "不支持的文件类型. 仅支持 JPEG、JPG、PNG",
  },
} as const;

export interface FileUploaderProps {
  files: FileItem[];
  onFilesAdded: (newFiles: FileItem[]) => void;
  config?: FileTypeConfig;
  configType?: keyof typeof FILE_TYPE_CONFIGS;
  className?: string;
  disabled?: boolean;
  placeholder?: {
    title?: string;
    dragText?: string;
  };
}
