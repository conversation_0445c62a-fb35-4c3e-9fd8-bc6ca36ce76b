import UploadIcon from "@/assets/upload.svg?react";
import { useAlert } from "@/hooks/useAlert";
import type { FileItem } from "@/types/knowledge";
import { cn } from "@/utils/utils";
import { useRef, useState } from "react";
import type { FileTypeConfig, FileUploaderProps } from "./types";
import { FILE_TYPE_CONFIGS } from "./types";
import { createFileItem, formatFileSize } from "./utils";

// 验证文件函数
function validateFile(
  file: File,
  config: FileTypeConfig,
): { valid: boolean; reason?: string } {
  // 检查文件类型
  const fileExtension = "." + file.name.split(".").pop()?.toLowerCase();
  if (
    !config.allowedTypes.includes(file.type) &&
    !config.allowedExtensions.includes(fileExtension)
  ) {
    return {
      valid: false,
      reason: `${config.errorMessage}: ${fileExtension}`,
    };
  }

  // 检查文件大小
  if (file.size > config.maxFileSize) {
    return {
      valid: false,
      reason: `文件大小超出限制: ${formatFileSize(file.size)}. 最大允许 ${formatFileSize(config.maxFileSize)}`,
    };
  }

  return { valid: true };
}

// 导出类型和配置
export { FILE_TYPE_CONFIGS } from "./types";
export type { FileItem, FileTypeConfig, FileUploaderProps } from "./types";
export * from "./utils";

export default function FileUploader({
  files,
  onFilesAdded,
  config,
  configType = "DOCUMENT",
  className,
  disabled = false,
  placeholder,
}: FileUploaderProps) {
  const alert = useAlert();
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropAreaRef = useRef<HTMLDivElement>(null);

  // 使用传入的配置或预定义配置
  const fileConfig = config || FILE_TYPE_CONFIGS[configType];
  const isMultiFile = (fileConfig.maxFiles ?? 1) > 1;
  const maxFiles = fileConfig.maxFiles ?? 1;

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const processFiles = (fileList: FileList | null) => {
    if (!fileList || disabled) return;

    if (isMultiFile && files.length >= maxFiles) {
      alert.error(`最多只能添加${maxFiles}个文件`);
      return;
    }

    const newFiles: FileItem[] = [];
    const currentCount = files.length;
    const remainingSlots = isMultiFile ? maxFiles - currentCount : 1;
    const filesToProcess = Math.min(fileList.length, remainingSlots);

    for (let i = 0; i < filesToProcess; i++) {
      const file = fileList[i];
      const validation = validateFile(file, fileConfig);

      if (!validation.valid) {
        alert.error(validation.reason || "文件验证失败");
        continue;
      }

      const fileItem = createFileItem(file, i);
      newFiles.push(fileItem);
    }

    if (newFiles.length > 0) {
      onFilesAdded(newFiles);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (disabled) return;

    if (isMultiFile && files.length >= maxFiles) {
      return;
    }

    processFiles(e.dataTransfer.files);
  };

  const handleFileSelect = () => {
    if (disabled) return;

    if (isMultiFile && files.length >= maxFiles) {
      alert.error(`最多只能添加${maxFiles}个文件`);
      return;
    }
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    processFiles(e.target.files);
  };

  // 判断是否达到文件数量限制
  const isMaxFilesReached = isMultiFile && files.length >= maxFiles;

  return (
    <div
      ref={dropAreaRef}
      className={cn(
        "flex w-full cursor-pointer flex-col items-center justify-center gap-2 rounded-xl border border-dashed p-6 transition-colors",
        isDragging
          ? isMaxFilesReached
            ? "border-red-1 bg-red-3"
            : "border-primary-default bg-bg-primary-2"
          : "border-border-1 bg-bg-light-3",
        isMaxFilesReached || disabled ? "cursor-not-allowed opacity-50" : "",
        className,
      )}
      onClick={isMaxFilesReached || disabled ? undefined : handleFileSelect}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        onChange={handleFileInputChange}
        multiple={isMultiFile}
        accept={fileConfig.allowedExtensions.join(",")}
        disabled={disabled}
      />
      <UploadIcon className="h-[120px] w-[120px]" />
      <p className="text-base font-medium text-text-1">
        {placeholder?.title || "点击选择文件或将文件拖放到此处"}
      </p>
      <div className="flex min-h-[60px] flex-col items-center justify-center text-xs leading-5 text-text-4">
        {isDragging ? (
          isMaxFilesReached ? (
            <p className="text-sm text-red-1">一次最多上传 {maxFiles} 个文件</p>
          ) : (
            <p className="text-sm text-primary-default">
              {placeholder?.dragText || "松手进行添加"}
            </p>
          )
        ) : (
          <>
            {fileConfig.description.map((desc: string, index: number) => (
              <p key={index}>{desc}</p>
            ))}
          </>
        )}
      </div>
    </div>
  );
}
