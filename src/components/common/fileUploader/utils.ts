import type { FileItem } from "./types";

// 工具函数
export function formatFileSize(bytes: number): string {
  if (bytes < 1024) return bytes + " B";
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + " KB";
  return (bytes / (1024 * 1024)).toFixed(1) + " MB";
}

export function getFileNameAndExtension(fileName: string): [string, string] {
  const lastDotIndex = fileName.lastIndexOf(".");
  const name = fileName.substring(0, lastDotIndex);
  const extension = fileName.substring(lastDotIndex);
  return [name, extension];
}

export function createFileItem(file: File, index = 0): FileItem {
  const [name, extension] = getFileNameAndExtension(file.name);

  return {
    id: `file-${Date.now()}-${index}`,
    size: file.size,
    type: file.type,
    file: file,
    progress: 0,
    name,
    extension,
  };
}
