import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lide<PERSON>Thumb,
  SliderTrack,
} from "@radix-ui/react-slider";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import type { FileItem } from "../fileUploader";

export interface AvatarPreviewerProps {
  files: FileItem[];
  onRemoveFile: (id: string) => void;
  onSavePreview: (image: string) => void;
}

const minZoom = 0.5;
const maxZoom = 3;
const stepZoom = 0.1;

const AvatarPreviewer = forwardRef(
  ({ files, onRemoveFile, onSavePreview }: AvatarPreviewerProps, ref) => {
    const [preview, setPreview] = useState<FileReader["result"]>(null);
    const [zoom, setZoom] = useState<number>(1);
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState<{ x: number; y: number }>({
      x: 0,
      y: 0,
    });

    const imageContainerRef = useRef<HTMLDivElement>(null);
    const imageRef = useRef<HTMLImageElement>(null);

    // 生成预览
    useEffect(() => {
      if (!files[0].file) {
        setPreview(null);
        return;
      }

      // 文件改变，重置状态
      setZoom(1);
      setPosition({ x: 0, y: 0 });

      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result);
      };
      reader.readAsDataURL(files[0].file);
    }, [files]);

    // 图片内容拖拽
    const handleMouseDown = (e: React.MouseEvent<HTMLImageElement>) => {
      e.preventDefault();
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y,
      });
    };

    const handleMouseMove = (e: React.MouseEvent<HTMLImageElement>) => {
      if (!isDragging) return;
      e.preventDefault();

      const newX = e.clientX - dragStart.x;
      const newY = e.clientY - dragStart.y;

      if (imageRef.current && imageContainerRef.current) {
        const containerRect = imageContainerRef.current.getBoundingClientRect();
        const imgNaturalWidth = imageRef.current.naturalWidth;
        const imgNaturalHeight = imageRef.current.naturalHeight;

        // 计算图片实际显示尺寸（考虑缩放）
        const displayedWidth = imgNaturalWidth * zoom;
        const displayedHeight = imgNaturalHeight * zoom;

        // 计算最大可移动范围（考虑图片可能比容器小的情况）
        const maxX = Math.max(0, (displayedWidth - containerRect.width) / 2);
        const maxY = Math.max(0, (displayedHeight - containerRect.height) / 2);

        // 如果图片比容器小，则不允许移动
        const canMoveX = displayedWidth > containerRect.width;
        const canMoveY = displayedHeight > containerRect.height;

        setPosition({
          x: canMoveX ? Math.max(-maxX, Math.min(maxX, newX)) : 0,
          y: canMoveY ? Math.max(-maxY, Math.min(maxY, newY)) : 0,
        });
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    const handleZoomChange = (value: number[]) => {
      setZoom(value[0]);
      // 缩放时重置位置到中心
      setPosition({ x: 0, y: 0 });
    };

    // 取消上传
    const handleCancel = () => {
      onRemoveFile(files[0].id);
      setPreview(null);
    };

    // 保存 (模拟)
    const handleSave = () => {
      if (!preview) return;

      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const img = new Image();

      img.onload = () => {
        const container = imageContainerRef.current;
        const imgContainer = imageRef.current;
        if (!container || !ctx || !imgContainer) return;

        // 获取容器尺寸（圆形直径）
        const containerRect = container.getBoundingClientRect();
        const imgContainerRect = imgContainer.getBoundingClientRect();
        const diameter = Math.min(containerRect.width, containerRect.height);
        const radius = diameter / 2;

        // 设置画布大小
        canvas.width = diameter;
        canvas.height = diameter;

        // 清除画布
        ctx.clearRect(0, 0, diameter, diameter);

        // 创建圆形裁剪路径
        ctx.beginPath();
        ctx.arc(radius, radius, radius, 0, Math.PI * 2);
        ctx.closePath();
        ctx.clip(); // 应用圆形裁剪区域

        // 计算图片实际显示尺寸
        const imgWidth = imgContainerRect.width;
        const imgHeight = imgContainerRect.height;

        // 计算图片在容器中的位置
        const offsetX = (containerRect.width - imgWidth) / 2 + position.x;
        const offsetY = (containerRect.height - imgHeight) / 2 + position.y;

        // 绘制图片（与预览完全一致的位置）
        ctx.drawImage(
          img,
          0,
          0,
          img.naturalWidth,
          img.naturalHeight, // 源图像全部
          offsetX,
          offsetY,
          imgWidth,
          imgHeight, // 目标位置和尺寸
        );

        // 获取最终的圆形图片数据
        const finalImage = canvas.toDataURL("image/png"); // 使用PNG保持透明度

        // 如果需要JPEG格式（无透明背景）
        // const finalImage = canvas.toDataURL('image/jpeg', 0.9);

        console.log("圆形裁剪后的图片:", finalImage);
        onSavePreview(finalImage);
      };

      img.src = preview as string;
    };

    useImperativeHandle(ref, () => ({
      handleSave,
      handleCancel,
    }));

    if (files.length === 0) {
      return null;
    }

    return (
      <div>
        <div className="relative mx-auto mb-6 h-[212px] w-[212px] rounded-lg border border-dashed border-bg-light-1 bg-bg-light-3">
          {/* 图片裁剪容器 */}
          <div
            ref={imageContainerRef}
            className="relative h-full w-full overflow-hidden rounded-full bg-bg-light-1"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            <img
              ref={imageRef}
              src={preview as string}
              alt="头像预览"
              className="absolute cursor-move"
              onDragStart={(e) => e.preventDefault()}
              style={{
                width: `${100 * zoom}%`,
                height: `${100 * zoom}%`,
                left: `calc(50% + ${position.x}px)`,
                top: `calc(50% + ${position.y}px)`,
                transform: "translate(-50%, -50%)",
                maxWidth: "none",
                maxHeight: "none",
                objectFit: "contain", // 裁剪'cover',
              }}
            />
          </div>
        </div>

        {/* 缩放控制 */}
        <div>
          <div className="flex items-center gap-2">
            <span
              className="cursor-pointer text-base font-medium text-text-1"
              onClick={() => setZoom(zoom - 0.1)}
            >
              -
            </span>
            <Slider
              defaultValue={[1]}
              min={minZoom}
              max={maxZoom}
              step={stepZoom}
              value={[zoom]}
              onValueChange={handleZoomChange}
              className="relative flex h-5 w-full touch-none select-none items-center"
            >
              <SliderTrack className="relative h-1 grow rounded-full bg-gray-200">
                <SliderRange className="absolute h-full rounded-full bg-primary-default" />
              </SliderTrack>
              <SliderThumb className="block h-3 w-3 rounded-full border-[1.2px] border-[#fff] bg-bg-primary-1 shadow-slider-track focus:outline-none focus:ring-2 focus:ring-blue-500" />
            </Slider>
            <span
              className="cursor-pointer text-base font-medium text-text-1"
              onClick={() => setZoom(zoom + 0.1)}
            >
              +
            </span>
          </div>
        </div>
      </div>
    );
  },
);

export default AvatarPreviewer;
