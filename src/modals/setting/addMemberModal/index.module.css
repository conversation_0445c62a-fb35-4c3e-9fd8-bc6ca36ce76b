/* 添加成员弹窗样式 */
.add-member-modal .ant-modal-header {
  border-bottom: none;
  padding: 24px 24px 0 24px;
}

.add-member-modal .ant-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.add-member-modal .ant-modal-body {
  padding: 24px;
}

.add-member-modal .ant-modal-footer {
  border-top: none;
  padding: 0 24px 24px 24px;
  text-align: right;
}

/* 表单标签样式 */
.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-label .required {
  color: #ef4444;
  margin-left: 2px;
}

/* 输入框样式 */
.add-member-modal .ant-input,
.add-member-modal .ant-select-selector {
  height: 40px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  font-size: 14px;
}

.add-member-modal .ant-input:focus,
.add-member-modal .ant-select-focused .ant-select-selector {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.add-member-modal .ant-select-selector {
  padding: 0 11px;
}

.add-member-modal .ant-select-selection-placeholder {
  color: #9ca3af;
}

/* 密码输入框和生成按钮的容器 */
.password-container {
  display: flex;
  gap: 8px;
}

.password-container .ant-input-affix-wrapper {
  flex: 1;
}

.password-container .ant-btn {
  height: 40px;
  padding: 0 16px;
  border-radius: 6px;
  font-size: 14px;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  color: #374151;
}

.password-container .ant-btn:hover {
  background-color: #e5e7eb;
  border-color: #9ca3af;
}

/* 按钮样式 */
.add-member-modal .ant-btn {
  height: 36px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.add-member-modal .ant-btn-default {
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  color: #374151;
}

.add-member-modal .ant-btn-default:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.add-member-modal .ant-btn-primary {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.add-member-modal .ant-btn-primary:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}

/* 表单项间距 */
.form-item {
  margin-bottom: 24px;
}

.form-item:last-child {
  margin-bottom: 0;
}
