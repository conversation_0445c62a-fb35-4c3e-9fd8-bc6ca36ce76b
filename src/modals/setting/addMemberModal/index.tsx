import { Button, Input, Modal, Select } from "antd";
import { EyeInvisibleOutlined, EyeTwoTone } from "@ant-design/icons";
import { useState } from "react";
import styles from "./index.module.css";

export interface AddMemberModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (values: MemberFormData) => void;
}

export interface MemberFormData {
  username: string;
  email: string;
  phone: string;
  department: string;
  role: string;
  password: string;
}

export default function AddMemberModal({ open, onCancel, onOk }: AddMemberModalProps) {
  const [formData, setFormData] = useState<MemberFormData>({
    username: "",
    email: "",
    phone: "",
    department: "",
    role: "",
    password: "",
  });

  // 部门选项
  const departmentOptions = [
    { label: "技术研发部", value: "技术研发部" },
    { label: "产品部", value: "产品部" },
    { label: "市场部", value: "市场部" },
    { label: "人事部", value: "人事部" },
  ];

  // 角色选项
  const roleOptions = [
    { label: "成员", value: "成员" },
    { label: "管理员", value: "管理员" },
    { label: "所有者", value: "所有者" },
  ];

  // 生成随机密码
  const generatePassword = () => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let password = "";
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setFormData({ ...formData, password });
  };

  // 处理表单提交
  const handleOk = () => {
    // 简单验证
    if (!formData.username || !formData.email || !formData.department || !formData.role || !formData.password) {
      return;
    }
    onOk(formData);
  };

  // 处理取消
  const handleCancel = () => {
    setFormData({
      username: "",
      email: "",
      phone: "",
      department: "",
      role: "",
      password: "",
    });
    onCancel();
  };

  return (
    <Modal
      title="添加成员"
      open={open}
      onCancel={handleCancel}
      width={600}
      className={`${styles['add-member-modal']} add-member-modal`}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleOk}>
          创建成员
        </Button>,
      ]}
    >
      <div>
        {/* 用户名 */}
        <div className={styles['form-item']}>
          <label className={styles['form-label']}>
            用户名 <span className={styles['required']}>*</span>
          </label>
          <Input
            placeholder="张三"
            value={formData.username}
            onChange={(e) => setFormData({ ...formData, username: e.target.value })}
          />
        </div>

        {/* 邮箱 */}
        <div className={styles['form-item']}>
          <label className={styles['form-label']}>
            邮箱 <span className={styles['required']}>*</span>
          </label>
          <Input
            placeholder="<EMAIL>"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          />
        </div>

        {/* 手机号 */}
        <div className={styles['form-item']}>
          <label className={styles['form-label']}>手机号</label>
          <Input
            placeholder="18312341234"
            value={formData.phone}
            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
          />
        </div>

        {/* 部门 */}
        <div className={styles['form-item']}>
          <label className={styles['form-label']}>
            部门 <span className={styles['required']}>*</span>
          </label>
          <Select
            placeholder="技术研发部"
            value={formData.department}
            onChange={(value) => setFormData({ ...formData, department: value })}
            options={departmentOptions}
            className="w-full"
            size="large"
          />
        </div>

        {/* 角色 */}
        <div className={styles['form-item']}>
          <label className={styles['form-label']}>
            角色 <span className={styles['required']}>*</span>
          </label>
          <Select
            placeholder="成员"
            value={formData.role}
            onChange={(value) => setFormData({ ...formData, role: value })}
            options={roleOptions}
            className="w-full"
            size="large"
          />
        </div>

        {/* 初始密码 */}
        <div className={styles['form-item']}>
          <label className={styles['form-label']}>
            初始密码 <span className={styles['required']}>*</span>
          </label>
          <div className={styles['password-container']}>
            <Input.Password
              placeholder="*******"
              value={formData.password}
              onChange={(e) => setFormData({ ...formData, password: e.target.value })}
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
            <Button onClick={generatePassword}>
              生成
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}
