import { useEffect, useRef } from "react";

import AvatarPreviewer from "@/components/common/avatarPreview";
import FileUploader, { type FileItem } from "@/components/common/fileUploader";
import Modal from "@/components/main/modal";
import { useObjectArray } from "@/hooks/common/useObjectArray";

type AvatarModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  title: string;
  submitting: boolean;
  onSubmit: (image: string) => void;
};

interface FilePreviewRef {
  handleSave: () => void;
  handleCancel: () => void;
}

export default function AvatarModal({
  open,
  setOpen,
  title,
  submitting,
  onSubmit,
}: AvatarModalProps) {
  const previewerRef = useRef<FilePreviewRef>(null);

  const {
    data: files,
    add: addFiles,
    remove: removeFile,
    clear: clearFiles,
  } = useObjectArray<FileItem>();

  // 当模态框关闭时重置状态
  useEffect(() => {
    if (!open) {
      setTimeout(() => {
        previewerRef.current?.handleCancel();
        clearFiles();
      }, 300);
    }
  }, [clearFiles, open]);

  const handleSubmit = () => {
    if (open && previewerRef.current) {
      previewerRef.current?.handleSave();
    }
  };

  const handleSaveImage = (image: string) => {
    onSubmit(image);
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      className="h-auto w-[400px]"
      onSubmit={handleSubmit}
    >
      <Modal.Header className="px-6 py-3 text-base font-medium text-text-1">
        {title}
      </Modal.Header>
      <Modal.Content className="px-6 py-4">
        {files.length > 0 ? (
          <AvatarPreviewer
            ref={previewerRef}
            files={files}
            onRemoveFile={removeFile}
            onSavePreview={handleSaveImage}
          />
        ) : (
          <FileUploader
            files={files}
            onFilesAdded={addFiles}
            configType="IMAGE"
          />
        )}
      </Modal.Content>
      <Modal.Footer
        submit={{
          submitLabel: "保存",
          loading: submitting,
          disabled: !files.length,
        }}
      />
    </Modal>
  );
}
