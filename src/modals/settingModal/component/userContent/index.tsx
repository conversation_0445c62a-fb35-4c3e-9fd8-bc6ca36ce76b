import { Button } from "@/components/main/button";
import { AuthContext } from "@/contexts/authContext";
import { useModal } from "@/hooks/useModal";
import { Users } from "@/types/api";
import { Avatar, Divider } from "antd";
import React, { useContext } from "react";
import NewPasswordModal from "./usePasswordModal";
import NewPhoneModal from "./usePhoneModal";
import NewUserIconModal from "./useUserIconModal";
import NewUsernameModal from "./useUsernameModal";

export default function UserContent() {
  const { userData } = useContext(AuthContext);

  const usernameModal = useModal<Users | null>();
  const phoneModal = useModal<Users | null>();
  const passwordModal = useModal<Users | null>();
  const userIconModal = useModal<Users | null>();

  const openUsernameModal = () => {
    usernameModal.open();
  };

  const openPhoneModal = () => {
    phoneModal.open();
  };

  const openPasswordModal = () => {
    passwordModal.open();
  };

  const openUserIconModal = () => {
    userIconModal.open();
  };

  return (
    <div className="h-full w-[1000px] overflow-auto">
      <div className="ml-6 mt-3 text-base font-medium text-text-1">
        账号信息
      </div>
      <div className="gap-6e flex flex-row px-6 py-3">
        <div className="flex h-full w-[128px] justify-end pr-4">
          <div className="flex w-[51px] flex-col items-center gap-2">
            <Avatar
              className="flex h-[48px] w-[48px] items-center justify-center rounded-[48px] border-[2.4px] border-border-1 bg-bg-green-1 text-[24px] font-medium text-primary-default"
              style={{ fontSize: 24 }}
              size={48}
              src={userData?.avatar}
            >
              {userData?.nickname?.slice(0, 1).toUpperCase()}
            </Avatar>
            <Button variant="outline" size="xs" onClick={openUserIconModal}>
              修改
            </Button>
          </div>
        </div>
        <div className="flex w-[728px] flex-col gap-6">
          <div className="text-base font-medium text-text-1">基本信息</div>
          <div className="flex w-full flex-col gap-2">
            <div className="text-sm font-medium text-text-1">用户名</div>
            <div className="flex flex-row justify-between font-normal text-text-2">
              <span>{userData?.nickname}</span>
              <Button variant="outline" size="xs" onClick={openUsernameModal}>
                修改
              </Button>
            </div>
          </div>
          <div className="flex w-full flex-col gap-2">
            <div className="text-sm font-medium text-text-1">邮箱</div>
            <div className="flex flex-row justify-between font-normal text-text-2">
              <span>{userData?.email}</span>
            </div>
          </div>
          <div className="flex w-full flex-col gap-2">
            <div className="text-sm font-medium text-text-1">手机号</div>
            <div className="flex flex-row justify-between font-normal text-text-2">
              <span>{userData?.phone}</span>
              <Button variant="outline" size="xs" onClick={openPhoneModal}>
                变更
              </Button>
            </div>
          </div>
          <Divider />
          <div className="text-base font-medium text-text-1">安全设置</div>
          <div className="flex w-full flex-col gap-2">
            <div className="text-sm font-medium text-text-1">密码</div>
            <div className="flex flex-row justify-between font-normal text-text-2">
              <span>********</span>
              <Button variant="outline" size="xs" onClick={openPasswordModal}>
                修改
              </Button>
            </div>
          </div>
        </div>
      </div>

      <NewUsernameModal
        open={usernameModal.visible}
        setOpen={(open) => usernameModal.toggle(open)}
      />

      <NewPhoneModal
        open={phoneModal.visible}
        setOpen={(open) => phoneModal.toggle(open)}
      />

      <NewPasswordModal
        open={passwordModal.visible}
        setOpen={(open) => passwordModal.toggle(open)}
      />

      <NewUserIconModal
        open={userIconModal.visible}
        setOpen={(open) => userIconModal.toggle(open)}
      />
    </div>
  );
}
