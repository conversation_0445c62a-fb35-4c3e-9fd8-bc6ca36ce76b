import * as Form from "@radix-ui/react-form";
import { useContext, useEffect, useState } from "react";

import { Input } from "@/components/main/input";
import Modal from "@/components/main/modal";
import { AuthContext } from "@/contexts/authContext";
import { usePostSaveUser } from "@/controllers/API/queries/userInfo";
import { useAlert } from "@/hooks/useAlert";

type NewUsernameModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

export default function NewUsernameModal({
  open,
  setOpen,
}: NewUsernameModalProps) {
  const { userData, getUser } = useContext(AuthContext);

  const [name, setName] = useState(userData?.nickname ?? "");
  const [isSaving, setIsSaving] = useState(false);

  const alert = useAlert();
  const { mutate: mutateSaveUser } = usePostSaveUser();

  useEffect(() => {
    if (open) {
      setName(userData?.nickname ?? "");
    }
  }, [userData, open]);

  const handleSubmit = () => {
    if (open) {
      handleUpdate();
    }
  };

  const handleUpdate = () => {
    setIsSaving(true);

    mutateSaveUser(
      {
        nickname: name,
      },
      {
        onSuccess: () => {
          alert.success("保存成功");
          setOpen(false);
          setIsSaving(false);
          getUser();
        },
        onError: () => {
          alert.error("保存失败");
          setIsSaving(false);
        },
      },
    );
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      className="h-auto w-[352px]"
      onSubmit={handleSubmit}
    >
      <Modal.Header className="px-6 py-3 text-base font-medium text-text-1">
        修改用户名
      </Modal.Header>
      <Modal.Content>
        <Form.Field name="name" className="relative">
          <Form.Label className="mb-2 inline-block text-sm font-medium after:ml-0.5 after:text-red-1 after:content-['*']">
            用户名
          </Form.Label>
          <Form.Control asChild>
            <Input
              placeholder="请输入用户名"
              value={name}
              maxLength={24}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </Form.Control>
        </Form.Field>
      </Modal.Content>
      <Modal.Footer submit={{ submitLabel: "保存", loading: isSaving }} />
    </Modal>
  );
}
