export interface FileItem {
  id: string;
  name: string;
  size: number;
  type: string;
  file: File;
  progress: number;
  extension: string;
  error?: boolean;
  uploaded?: boolean;
}

export const fileTypesAllowed = [
  "image/jpeg", // jpeg
  "image/jpg", // jpg
  "image/png", // png
];

export const fileExtensionsAllowed = [".jpeg", ".jpg", ".png"];

export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

export function formatFileSize(bytes: number): string {
  if (bytes < 1024) return bytes + " B";
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + " KB";
  return (bytes / (1024 * 1024)).toFixed(1) + " MB";
}

export function validateFile(file: File): { valid: boolean; reason?: string } {
  // 检查文件类型
  const fileExtension = "." + file.name.split(".").pop()?.toLowerCase();
  if (
    !fileTypesAllowed.includes(file.type) &&
    !fileExtensionsAllowed.includes(fileExtension)
  ) {
    return {
      valid: false,
      reason: `不支持的文件类型: ${fileExtension}. 仅支持 JPEG、JPG、PNG`,
    };
  }

  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      reason: `文件大小超出限制: ${formatFileSize(file.size)}. 最大允许 ${formatFileSize(MAX_FILE_SIZE)}`,
    };
  }

  return { valid: true };
}
