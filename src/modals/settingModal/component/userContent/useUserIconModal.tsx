import { useContext, useEffect, useRef, useState } from "react";

import AvatarPreviewer from "@/components/common/avatarPreview";
import FileUploader from "@/components/common/fileUploader";
import Modal from "@/components/main/modal";
import { AuthContext } from "@/contexts/authContext";
import { usePostSaveUser } from "@/controllers/API/queries/userInfo";
import { useFile } from "@/hooks/knowledge/useFile";
import { useAlert } from "@/hooks/useAlert";

type NewUserIconModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

interface FilePreviewRef {
  handleSave: () => void;
  handleCancel: () => void;
}

export default function NewUserIconModal({
  open,
  setOpen,
}: NewUserIconModalProps) {
  const { getUser } = useContext(AuthContext);

  const [isSaving, setIsSaving] = useState(false);
  const { files, addFiles, removeFile, clearFiles } = useFile();

  const filePreviewRef = useRef<FilePreviewRef>(null);

  const alert = useAlert();
  const { mutate: mutateSaveUser } = usePostSaveUser();

  // 重置状态当模态框关闭时
  useEffect(() => {
    if (!open) {
      setTimeout(() => {
        filePreviewRef.current?.handleCancel();
        clearFiles();
      }, 300);
    }
  }, [clearFiles, open]);

  const handleSubmit = () => {
    if (open && filePreviewRef.current) {
      filePreviewRef.current?.handleSave();
    }
  };

  const handleUpdate = (image: string) => {
    setIsSaving(true);

    mutateSaveUser(
      {
        avatar: image,
      },
      {
        onSuccess: () => {
          alert.success("保存成功");
          setOpen(false);
          setIsSaving(false);
          getUser();
        },
        onError: () => {
          alert.error("保存失败");
          setIsSaving(false);
        },
      },
    );
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      className="h-auto w-[352px]"
      onSubmit={handleSubmit}
    >
      <Modal.Header className="px-6 py-3 text-base font-medium text-text-1">
        修改头像
      </Modal.Header>
      <Modal.Content className="px-6 py-4">
        {files.length > 0 ? (
          <AvatarPreviewer
            ref={filePreviewRef}
            files={files}
            onRemoveFile={removeFile}
            onSavePreview={handleUpdate}
          />
        ) : (
          <FileUploader
            files={files}
            onFilesAdded={addFiles}
            configType="IMAGE"
          />
        )}
      </Modal.Content>
      <Modal.Footer
        submit={{
          submitLabel: "保存",
          loading: isSaving,
          disabled: !files.length,
        }}
      />
    </Modal>
  );
}
