import { Button, Input, Pagination, Space, Table, TableProps, Divider } from "antd";
import { useState } from "react";

import { Badge, BadgeVariant } from "@/components/main/badge";
import { SearchOutlined } from "@ant-design/icons";

interface DataType {
  key: string;
  name: string;
  mail: string;
  department: string;
  role: string;
  status: "有效" | "待激活";
  joinTime: string;
}

function MemberTable() {
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [searchValue, setSearchValue] = useState("");

  // 模拟数据，根据设计稿内容
  const data: DataType[] = Array.from({ length: 5 }, (_, index) => ({
    key: `${index + 1}`,
    name: "张三",
    mail: "<EMAIL>",
    department: "技术研发部",
    role: index === 0 ? "所有者" : index === 1 ? "管理员" : "成员",
    status: index % 3 === 1 ? "待激活" : "有效",
    joinTime: "2023",
  }));

  const columns: TableProps<DataType>["columns"] = [
    {
      title: "姓名",
      dataIndex: "name",
      key: "name",
      fixed: "left",
      width: 120,
    },
    {
      title: "邮箱",
      dataIndex: "mail",
      key: "mail",
      width: 200,
    },
    {
      title: "部门",
      dataIndex: "department",
      key: "department",
      width: 120,
    },
    {
      title: "角色",
      key: "role",
      dataIndex: "role",
      width: 100,
      render: (text) => <Badge>{text}</Badge>,
    },
    {
      title: "状态",
      key: "status",
      dataIndex: "status",
      width: 100,
      render: (status: "有效" | "待激活") => (
        <Badge
          variant={status === "有效" ? BadgeVariant.SUCCESS : BadgeVariant.WARN}
          showDot={true}
        >
          {status}
        </Badge>
      ),
    },
    {
      title: "加入时间",
      key: "joinTime",
      dataIndex: "joinTime",
      width: 100,
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: () => (
        <Space size={0} split={<Divider type="vertical" />}>
          <Button type="link" size="small" className="p-0">
            编辑
          </Button>
          <Button type="link" size="small" className="p-0">
            改密
          </Button>
          <Button type="link" size="small" className="p-0">
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleSearch = (value: string) => {
    setSearchValue(value);
    // 这里可以添加搜索逻辑
  };

  const handleAddMember = () => {
    // 添加成员逻辑
    console.log("添加成员");
  };

  const handlePageChange = (page: number, size?: number) => {
    setCurrent(page);
    if (size) {
      setPageSize(size);
    }
  };

  return (
    <div className="flex h-full w-full flex-col gap-2">
      {/* 表格头部 */}
      <div className="flex h-6 items-center justify-between">
        <span className="text-base font-medium">AIWorks 团队</span>
        <div style={{ display: "flex", gap: "12px", alignItems: "center" }}>
          <Input
            className="w-[240px]"
            placeholder="搜索提示..."
            prefix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
            value={searchValue}
            onChange={(e) => handleSearch(e.target.value)}
            allowClear
            size="small"
          />
          <Button onClick={handleAddMember} size="small" className="text-xs">
            添加成员
          </Button>
        </div>
      </div>

      {/* 表格 */}
      <div className="flex min-h-0 flex-1 flex-col overflow-hidden rounded-md">
        <Table<DataType>
          columns={columns}
          dataSource={data.slice((current - 1) * pageSize, current * pageSize)}
          scroll={{ x: 800, y: 440 }}
          size="small"
          style={{
            backgroundColor: "#fff",
          }}
          className="ant-table-container-full-fill"
          pagination={false}
          footer={() => (
            <Pagination
              current={current}
              pageSize={pageSize}
              total={data.length}
              onChange={handlePageChange}
              showSizeChanger={false}
              showQuickJumper={false}
              showTotal={(total) => `共 ${total} 条数据，每页 ${pageSize} 条`}
              size="small"
              align="end"
            />
          )}
        />
        {/* 分页器 */}
        {/* <div
          className="flex h-11 items-center justify-end"
          style={{
            borderTop: "1px solid #f0f0f0",
            backgroundColor: "#fff",
          }}
        >
          <Pagination
            current={current}
            pageSize={pageSize}
            total={data.length}
            onChange={handlePageChange}
            showSizeChanger={false}
            showQuickJumper={false}
            showTotal={(total) => `共 ${total} 条数据，每页 ${pageSize} 条`}
            size="small"
          />
        </div> */}
      </div>
    </div>
  );
}

export default MemberTable;
