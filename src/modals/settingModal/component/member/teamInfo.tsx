import { Avatar } from "antd";

import { EditIcon } from "@/components/main/icon";
import { useModal } from "@/hooks/useModal";
import AvatarModal from "@/modals/avatarModal";
import TeamNameModal from "@/modals/teamNameModal";

export default function TeamInfo() {
  const avatarModal = useModal();
  const teamNameModal = useModal();

  const handleSubmitAvatar = (image: string) => {
    avatarModal.close();
    console.log("image", image);
  };

  return (
    <div className="flex h-[52px] items-center gap-2 px-6">
      <div className="group/avatar relative">
        <Avatar
          className="border-[1.4px] border-border-1 bg-bg-green-1 text-sm font-medium text-primary-default"
          size={28}
        >
          A
        </Avatar>
        <div className="absolute left-0 top-0 z-10 hidden h-[28px] w-[28px] rounded-[28px] bg-[rgba(0,0,0,0.30)] backdrop-blur-xs group-hover/avatar:block"></div>
        <EditIcon
          className="absolute left-1 top-1 z-20 hidden text-[20px] text-white group-hover/avatar:block"
          onClick={() => avatarModal.open()}
        />
      </div>

      <div className="group/name flex cursor-pointer items-center gap-2 text-sm font-medium">
        <span>AIWorks 团队</span>
        <EditIcon
          className="hidden text-[20px] text-text-2 group-hover/name:block"
          onClick={() => teamNameModal.open()}
        />
      </div>
      <AvatarModal
        title="修改团队 logo"
        open={avatarModal.visible}
        setOpen={(open) => avatarModal.toggle(open)}
        submitting={avatarModal.loading}
        onSubmit={handleSubmitAvatar}
      />
      <TeamNameModal
        open={teamNameModal.visible}
        setOpen={(open) => teamNameModal.toggle(open)}
      />
    </div>
  );
}
