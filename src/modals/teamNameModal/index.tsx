import * as Form from "@radix-ui/react-form";
import { useContext, useEffect, useState } from "react";

import { Input } from "@/components/main/input";
import Modal from "@/components/main/modal";
import { AuthContext } from "@/contexts/authContext";
import { useAlert } from "@/hooks/useAlert";

type TeamNameModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

export default function TeamNameModal({ open, setOpen }: TeamNameModalProps) {
  const { userData } = useContext(AuthContext);

  const [name, setName] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  const alert = useAlert();

  useEffect(() => {
    if (open) {
      setName(userData?.tenants?.[0].name ?? "");
    }
  }, [userData, open]);

  const handleSubmit = () => {
    setOpen(false);
    setIsSaving(false);
    console.log(name);

    alert.success("保存成功");
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      className="h-auto w-[352px]"
      onSubmit={handleSubmit}
    >
      <Modal.Header className="px-6 py-3 text-base font-medium text-text-1">
        修改团队名称
      </Modal.Header>
      <Modal.Content>
        <Form.Field name="name" className="relative">
          <Form.Label className="mb-2 inline-block text-sm font-medium after:ml-0.5 after:text-red-1 after:content-['*']">
            团队名称
          </Form.Label>
          <Form.Control asChild>
            <Input
              placeholder="请输入用户名"
              value={name}
              maxLength={24}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </Form.Control>
        </Form.Field>
      </Modal.Content>
      <Modal.Footer submit={{ submitLabel: "保存", loading: isSaving }} />
    </Modal>
  );
}
