# GitLab CI/CD 配置文件

# 缓存配置
cache:
  key:
    files:
      - package-lock.json
  paths:
    - .npm/
  policy: pull-push

# 流水线阶段
stages:
  - prepare
  - quality

# 准备阶段 - 安装依赖
install-dependencies:
  stage: prepare
  script:
    - echo "Node.js version:" && node --version
    - echo "npm version:" && npm --version
    - npm ci --cache .npm --prefer-offline
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - .npm/
      - node_modules/
    policy: pull-push
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# 代码质量检查阶段（并行执行）
.quality_template: &quality_template
  stage: quality
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - .npm/
      - node_modules/
    policy: pull
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# TypeScript 类型检查
check-types:
  <<: *quality_template
  script:
    - npm run check-types

# 代码格式检查
check-format:
  <<: *quality_template
  script:
    - npm run check-format

# TODO: 暂时不开启 eslint 阶段，项目中有许多原有的代码语法不符合规范，后续迭代中再排期陆续修复
# ESLint 语法检查
# lint:
#   <<: *quality_template
#   script:
#     - npm run lint
